import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { PMOAssessmentAgent } from '../../../lib/agents/pmo/PMOAssessmentAgent';
import { PMOFormInput, ModelProvider, AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
import { QueryDocumentsAgent } from 'components/Agents/QueryDocumentsAgent';
import { createLlmService } from '../../../lib/tools/llmServiceAdapter';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import { doc, getDoc, collection, query, where, getDocs, limit } from 'firebase/firestore';
import { db } from '../../../components/firebase';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { formData, userId } = body;

    // Validate request body
    if (!formData || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate that the authenticated user matches the requested userId
    if (session.user.email !== userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create the PMO assessment agent
    // Extract model provider and model name from the form data
    const modelProvider = formData.modelProvider || 'openai';
    const modelName = formData.modelName || 'gpt-4o';

    // Validate modelProvider to ensure it's a valid ModelProvider type
    const validModelProvider = (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
      ? modelProvider as ModelProvider
      : 'openai'; // Default to 'openai' if invalid or not provided

    // Create an LLM service adapter with the validated model provider
    const llmService = createLlmService(validModelProvider);

    const pmoAgent = new PMOAssessmentAgent(
      {
        userId,
        includeExplanation: false,
        streamResponse: false
      },
      llmService // Pass the LLM service as the second parameter
    );

    // Create and set the query documents agent
    const queryDocumentsAgent = new QueryDocumentsAgent({
      userId,
      includeExplanation: false
    });
    pmoAgent.setQueryDocumentsAgent(queryDocumentsAgent);

    // We don't need the StrategicDirectorAgent for this flow
    // Instead, we'll directly generate the requirements specification

    // First, we need to get the context data
    let contextData = '';
    let contextChunks: any[] = [];

    // Get context data from the form input
    if (formData.contextOptions) {
      if (formData.contextOptions.customContext) {
        // For custom context, create a synthetic chunk
        contextData = formData.contextOptions.customContext;
        contextChunks = [{
          content: formData.contextOptions.customContext,
          metadata: {
            fileName: 'Custom Context',
            source: 'User Input',
            relevance: 1.0
          },
          text: formData.contextOptions.customContext
        }];
        console.log(`PMO Assessment API: Created synthetic chunk for custom context`);
      } else if (formData.contextOptions.fileIds && formData.contextOptions.fileIds.length > 0) {
        // DIRECT APPROACH: Instead of using queryDocumentsAgent, let's directly fetch and process the document
        try {
          console.log(`PMO Assessment API: Directly fetching document content for file ID: ${formData.contextOptions.fileIds[0]}`);

          // Get the file from Firestore
          const fileId = formData.contextOptions.fileIds[0];
          const fileRef = doc(db, `users/${userId}/files`, fileId);
          const fileDoc = await getDoc(fileRef);

          if (fileDoc.exists()) {
            const fileData = fileDoc.data();
            console.log(`PMO Assessment API: Found file: ${fileData.name}`);

            // Get the document content
            let documentContent = '';

            // Check if the document has extracted text stored in Firestore
            if (fileData.contents) {
              documentContent = fileData.contents;
              console.log(`PMO Assessment API: Using content stored in Firestore`);
            } else if (fileData.textFilePath) {
              // If text is stored in Storage, fetch it
              console.log(`PMO Assessment API: Fetching content from Storage path: ${fileData.textFilePath}`);
              const storage = getStorage();
              const textFileRef = ref(storage, fileData.textFilePath);
              const textFileUrl = await getDownloadURL(textFileRef);

              // Fetch the text file
              const response = await fetch(textFileUrl);
              documentContent = await response.text();
            } else if (fileData.downloadUrl) {
              // If we only have the download URL, we'll use that as context
              console.log(`PMO Assessment API: No extracted text available, using file metadata as context`);
              documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}\nDownload URL: ${fileData.downloadUrl}`;
            }

            // Create context data and chunks
            contextData = documentContent;

            // Split the document into chunks for better processing
            const chunkSize = 2000; // Characters per chunk
            const chunks = [];

            for (let i = 0; i < documentContent.length; i += chunkSize) {
              const chunkContent = documentContent.substring(i, i + chunkSize);
              chunks.push({
                content: chunkContent,
                metadata: {
                  fileName: fileData.name,
                  source: fileData.category || 'Unknown',
                  relevance: 1.0 - (i / documentContent.length * 0.5) // Decreasing relevance for later chunks
                },
                text: chunkContent
              });
            }

            contextChunks = chunks;
            console.log(`PMO Assessment API: Created ${chunks.length} chunks from document content`);
          } else {
            console.error(`PMO Assessment API: File not found: ${fileId}`);
            // Create a fallback chunk with error information
            contextData = `Error: File with ID ${fileId} not found`;
            contextChunks = [{
              content: contextData,
              metadata: {
                fileName: 'Error',
                source: 'Error',
                relevance: 0.5
              },
              text: contextData
            }];
          }
        } catch (error) {
          console.error(`PMO Assessment API: Error fetching document:`, error);
          // Create a fallback chunk with error information
          const errorMessage = error instanceof Error ? error.message : String(error);
          contextData = `Error fetching document: ${errorMessage}`;
          contextChunks = [{
            content: contextData,
            metadata: {
              fileName: 'Error',
              source: 'Error',
              relevance: 0.5
            },
            text: contextData
          }];
        }
      } else if (formData.contextOptions.categoryIds && formData.contextOptions.categoryIds.length > 0) {
        // DIRECT APPROACH: Instead of using queryDocumentsByCategory, let's directly fetch files by category
        try {
          const categoryId = formData.contextOptions.categoryIds[0];
          console.log(`PMO Assessment API: Directly fetching files for category: ${categoryId}`);

          // Query Firestore for files in this category
          const filesRef = collection(db, `users/${userId}/files`);
          const q = query(filesRef, where('category', '==', categoryId), limit(5));
          const querySnapshot = await getDocs(q);

          if (!querySnapshot.empty) {
            console.log(`PMO Assessment API: Found ${querySnapshot.size} files in category ${categoryId}`);

            // Process each file
            const allChunks = [];
            let combinedContent = '';

            for (const doc of querySnapshot.docs) {
              const fileData = doc.data();
              console.log(`PMO Assessment API: Processing file: ${fileData.name}`);

              // Get the document content
              let documentContent = '';

              // Check if the document has extracted text stored in Firestore
              if (fileData.contents) {
                documentContent = fileData.contents;
              } else if (fileData.textFilePath) {
                // If text is stored in Storage, we'll just use the metadata for simplicity
                documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}`;
              } else if (fileData.downloadUrl) {
                // If we only have the download URL, we'll use that as context
                documentContent = `File: ${fileData.name}\nType: ${fileData.type}\nCategory: ${fileData.category}`;
              }

              // Add to combined content
              combinedContent += `\n\n--- FILE: ${fileData.name} ---\n\n${documentContent}`;

              // Create a chunk for this file
              allChunks.push({
                content: documentContent,
                metadata: {
                  fileName: fileData.name,
                  source: fileData.category || categoryId,
                  relevance: 0.9 // High relevance for category matches
                },
                text: documentContent
              });
            }

            // Set context data and chunks
            contextData = combinedContent.trim();
            contextChunks = allChunks;
            console.log(`PMO Assessment API: Created ${allChunks.length} chunks from category files`);
          } else {
            console.log(`PMO Assessment API: No files found in category ${categoryId}`);
            // Create a fallback chunk with information
            contextData = `No files found in category ${categoryId}`;
            contextChunks = [{
              content: contextData,
              metadata: {
                fileName: 'No Files Found',
                source: categoryId,
                relevance: 0.5
              },
              text: contextData
            }];
          }
        } catch (error) {
          console.error(`PMO Assessment API: Error fetching category files:`, error);
          // Create a fallback chunk with error information
          const errorMessage = error instanceof Error ? error.message : String(error);
          contextData = `Error fetching category files: ${errorMessage}`;
          contextChunks = [{
            content: contextData,
            metadata: {
              fileName: 'Error',
              source: 'Error',
              relevance: 0.5
            },
            text: contextData
          }];
        }
      }
    }

    // Ensure we always have at least one context chunk
    if (contextChunks.length === 0) {
      console.log(`PMO Assessment API: No context chunks created, adding a default chunk`);
      const defaultContent = formData.description;
      contextChunks = [{
        content: defaultContent,
        metadata: {
          fileName: 'Project Description',
          source: 'User Input',
          relevance: 1.0
        },
        text: defaultContent
      }];
      contextData = defaultContent;
    }

    // Log the context chunks for debugging
    console.log(`PMO Assessment API: Final context chunks count: ${contextChunks.length}`);
    if (contextChunks.length > 0) {
      contextChunks.forEach((chunk, index) => {
        const source = chunk.metadata?.fileName || chunk.metadata?.source || 'N/A';
        const relevance = chunk.metadata?.relevance ? ` (Relevance: ${(chunk.metadata.relevance * 100).toFixed(1)}%)` : '';
        console.log(`PMO Assessment API: Context Chunk ${index + 1} - [Section: ${source}${relevance}]`);
        console.log(`PMO Assessment API: Content preview: ${(chunk.content || chunk.text || 'No content').substring(0, 100)}...`);
      });
    }

    // Generate a simple assessment for team selection
    const simpleAssessment = `Project Title: ${formData.title}\nDescription: ${formData.description}\nPriority: ${formData.priority || 'Medium'}\nCategory: ${formData.category || 'Unknown'}`;

    // Select teams using PMOAgent's internal logic instead of StrategicDirectorAgent
    // For simplicity, we'll use a default team selection or determine based on keywords
    let selectedTeams: AgenticTeamId[] = [];

    // Simple keyword-based team selection
    const description = formData.description.toLowerCase();
    if (description.includes('market') || description.includes('brand') || description.includes('campaign')) {
      selectedTeams.push(AgenticTeamId.Marketing);
    } else if (description.includes('research') || description.includes('analysis') || description.includes('data')) {
      selectedTeams.push(AgenticTeamId.Research);
    } else if (description.includes('software') || description.includes('develop') || description.includes('code') || description.includes('app')) {
      selectedTeams.push(AgenticTeamId.SoftwareDesign);
    } else if (description.includes('sales') || description.includes('client') || description.includes('revenue')) {
      selectedTeams.push(AgenticTeamId.Sales);
    } else if (description.includes('business') || description.includes('process') || description.includes('strategy')) {
      selectedTeams.push(AgenticTeamId.BusinessAnalysis);
    } else {
      // Default to Research if no keywords match
      selectedTeams.push(AgenticTeamId.Research);
    }

    // Generate the requirements specification directly using our new public method
    const requirementsSpec = await pmoAgent.generateRequirementsSpecificationDirectly(
      formData as PMOFormInput,
      simpleAssessment,
      selectedTeams,
      contextChunks
    );

    // Return the requirements specification and selected teams
    return NextResponse.json({
      pmoAssessment: requirementsSpec, // Return the requirements spec as the assessment
      selectedTeams: selectedTeams
    });
  } catch (error: any) {
    console.error('Error generating PMO assessment:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate PMO assessment' },
      { status: 500 }
    );
  }
}
