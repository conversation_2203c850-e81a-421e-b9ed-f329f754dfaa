import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { ResearchTeamAgent } from '../../../lib/agents/research/ResearchTeamAgent';
import { Task, TaskPriority } from '../../../admin/planner/types';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';

// Extended Task interface for PMO operations that includes metadata
interface PMOTask extends Task {
  metadata?: {
    source?: string;
    pmoId?: string;
    pmoAssessment?: string;
    teamSelectionRationale?: string;
    autoTriggered?: boolean;
    triggerTimestamp?: string;
    context?: string;
    pmoContextCategories?: any;
    pmoCustomContext?: any;
    priority?: string;
    notificationId?: string;
    [key: string]: any;
  };
}

// Helper function to normalize priority values
function normalizePriority(priority: string): TaskPriority {
  const normalizedPriority = priority.trim().toLowerCase();

  if (normalizedPriority === 'low') return 'Low';
  if (normalizedPriority === 'medium') return 'Medium';
  if (normalizedPriority === 'high') return 'High';
  if (normalizedPriority === 'critical') return 'Critical';

  // Default to Medium if not recognized
  return 'Medium';
}

/**
 * API endpoint to notify teams about PMO requirements
 * Enhanced to automatically trigger marketing collaboration for Marketing team
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription,
      pmoAssessment,
      teamSelectionRationale,
      priority,
      category,
      userId,
      pmoRecord,
      metadata
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create team notification record
    const teamNotification = {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription: projectDescription || '',
      pmoAssessment: pmoAssessment || '',
      teamSelectionRationale: teamSelectionRationale || '',
      priority: priority || 'Medium',
      category,
      userId,
      status: 'Pending Strategic Plan',
      notifiedAt: new Date(),
      requiresStrategicPlan: true,
      strategicPlanCreated: false,
      metadata: {
        ...metadata,
        documentType: 'Team Notification',
        source: 'PMO'
      }
    };

    // Save to team notifications collection
    const notificationRef = await addDoc(
      collection(db, 'users', userId, 'teamNotifications'),
      teamNotification
    );

    // Also create a record in the team-specific collection for easy access
    const teamSpecificNotification = {
      ...teamNotification,
      notificationId: notificationRef.id,
      teamSpecific: true
    };

    await addDoc(
      collection(db, 'users', userId, `${teamName.toLowerCase().replace(/\s+/g, '')}TeamTasks`),
      teamSpecificNotification
    );

    let marketingCollaborationResult = null;
    let researchCollaborationResult = null;

    // If this is for the Marketing team, automatically trigger marketing collaboration
    if (teamName.toLowerCase() === 'marketing') {
      try {
        marketingCollaborationResult = await triggerMarketingCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (marketingError) {
        console.error('Error triggering marketing collaboration:', marketingError);
        // Don't fail the entire request if marketing collaboration fails
        // The notification was still created successfully
      }
    }

    // If this is for the Research team, automatically trigger research collaboration
    if (teamName.toLowerCase() === 'research') {
      try {
        researchCollaborationResult = await triggerResearchCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (researchError) {
        console.error('Error triggering research collaboration:', researchError);
        // Don't fail the entire request if research collaboration fails
        // The notification was still created successfully
      }
    }

    return NextResponse.json({
      success: true,
      notificationId: notificationRef.id,
      message: `Requirements sent to ${teamName} team successfully`,
      marketingCollaboration: marketingCollaborationResult,
      researchCollaboration: researchCollaborationResult
    });

  } catch (error) {
    console.error('Error notifying team:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to notify team'
      },
      { status: 500 }
    );
  }
}

/**
 * Trigger marketing collaboration workflow from PMO notification
 */
async function triggerMarketingCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  // Send the original user request description as the prompt for the marketing team
  // The PMO assessment and other details will be provided as context
  const marketingPrompt = pmoData.projectDescription;

  // Prepare comprehensive context from PMO assessment for the marketing team
  const contextInformation = `
# PMO Marketing Requirements Analysis Context

## Project Overview
**Title:** ${pmoData.projectTitle}
**Priority:** ${pmoData.priority}
**PMO ID:** ${pmoData.pmoId}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes:

1. **Strategic Marketing Assessment** - Analyze the marketing implications and opportunities
2. **Target Audience Analysis** - Identify and profile the target market segments
3. **Competitive Landscape** - Research and analyze competitive positioning
4. **Marketing Channel Strategy** - Recommend optimal marketing channels and tactics
5. **Content Strategy** - Develop content themes and messaging framework
6. **Campaign Planning** - Outline campaign structure and timeline
7. **Success Metrics** - Define KPIs and measurement framework
8. **Resource Requirements** - Estimate budget and resource needs

Please ensure your analysis is comprehensive and actionable, providing specific recommendations that the marketing team can implement.

## Additional Context
${pmoData.pmoRecord?.customContext ? `- Custom Context: ${pmoData.pmoRecord.customContext}` : ''}
${pmoData.pmoRecord?.contextCategories?.length ? `- Source Document Categories: ${pmoData.pmoRecord.contextCategories.join(', ')}` : ''}
  `.trim();

  // Use the first contextCategory as the primary category for document search
  const documentCategory = pmoData.pmoRecord?.contextCategories?.length > 0
    ? pmoData.pmoRecord.contextCategories[0]
    : pmoData.category;

  // Call the marketing-agent-collaboration API
  const collaborationBody = {
    prompt: marketingPrompt,
    modelProvider: 'openai',
    modelName: 'o3-2025-04-16',
    userId: pmoData.userId,
    context: contextInformation,
    category: documentCategory, // Use PMO's contextCategories for document search
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      recordTitle: pmoData.projectTitle, // Add PMO title for project naming
      projectTitle: pmoData.projectTitle, // Also add as projectTitle for compatibility
      notificationId: pmoData.notificationId,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null
    }
  };

  const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/marketing-agent-collaboration`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(collaborationBody)
  });

  if (!response.ok) {
    throw new Error(`Marketing collaboration API error: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();

  return {
    success: true,
    requestId: result.requestId,
    message: 'Marketing collaboration triggered successfully',
    collaborationData: result
  };
}

/**
 * Trigger research collaboration workflow from PMO notification
 */
async function triggerResearchCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  console.log(`📊 Triggering research collaboration for PMO ${pmoData.pmoId}...`);

  // Send the comprehensive PMO assessment as the primary task description for the research team
  // This ensures the Research Team receives the detailed PMO analysis to elaborate upon
  const researchPrompt = `# PMO Research Task: ${pmoData.projectTitle}

## Original User Request
${pmoData.projectDescription}

## PMO Assessment and Requirements Analysis
${pmoData.pmoAssessment}

## Research Team Assignment
${pmoData.teamSelectionRationale}

## Research Objectives
Based on the PMO assessment above, the Research Team is tasked with:

1. **Elaborating on PMO Findings**: Build upon and expand the detailed analysis provided by the PMO
2. **Comprehensive Research**: Conduct in-depth research on all aspects identified in the PMO assessment
3. **Strategic Implementation**: Develop actionable strategies and implementation plans based on PMO requirements
4. **Quality Enhancement**: Enhance the PMO analysis with additional research, data, and strategic insights
5. **Cross-team Coordination**: Identify opportunities for collaboration with other teams as outlined in the PMO assessment

## Expected Deliverables
- Comprehensive research analysis that builds upon the PMO assessment
- Strategic implementation recommendations aligned with PMO requirements
- Enhanced documentation with additional research findings
- Quality-assured outputs that meet PMO standards and expectations

The Research Team should treat the PMO assessment as the foundation and elaborate on every aspect with detailed research and strategic analysis.`;

  // Prepare comprehensive context from PMO assessment for the research team
  const contextInformation = `
# PMO Research Requirements Analysis Context

## Project Overview
- **PMO ID**: ${pmoData.pmoId}
- **Project Title**: ${pmoData.projectTitle}
- **Priority**: ${pmoData.priority}
- **Category**: ${pmoData.category}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Research Requirements
The Research team has been selected to provide comprehensive analysis and strategic insights for this PMO initiative. The research should focus on:

1. **Information Gathering**: Collect relevant data and sources related to the project scope
2. **Analysis and Synthesis**: Process information to identify key insights, patterns, and recommendations
3. **Strategic Planning**: Develop strategic implementation recommendations aligned with PMO objectives
4. **Quality Assurance**: Ensure all research outputs meet PMO standards and requirements
5. **Cross-team Coordination**: Identify opportunities for collaboration with other agentic teams

## Expected Deliverables
- Comprehensive research analysis report
- Strategic implementation plan (if applicable)
- Quality-assured findings and recommendations
- PMO-compliant documentation and citations

## Context Categories
${pmoData.pmoRecord?.contextCategories?.join(', ') || 'General Research'}

${pmoData.pmoRecord?.customContext ? `## Additional Context\n${pmoData.pmoRecord.customContext}` : ''}
`;

  // Determine document category for research context
  const documentCategory = pmoData.pmoRecord?.contextCategories?.[0] || pmoData.category || 'research';

  // Create PMO Task object for ResearchTeamAgent
  const pmoTask: PMOTask = {
    id: `${pmoData.pmoId}-${Date.now()}`,
    projectId: 'pmo-project', // Add required projectId field
    title: pmoData.projectTitle || 'PMO Research Task',
    description: researchPrompt,
    category: documentCategory || 'Research',
    priority: normalizePriority(pmoData.priority || 'medium'),
    status: 'In Progress',
    startDate: new Date(), // Add required startDate field
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default: 1 week from now
    assignedTo: [AgenticTeamId.Research],
    dependencies: [], // Add required dependencies field
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      pmoAssessment: pmoData.pmoAssessment,
      teamSelectionRationale: pmoData.teamSelectionRationale,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      context: contextInformation,
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null,
      priority: pmoData.priority,
      notificationId: pmoData.notificationId
    }
  };

  // Initialize ResearchTeamAgent directly (no API call needed)
  const researchTeamAgent = new ResearchTeamAgent({
    userId: pmoData.userId,
    includeExplanation: true,
    streamResponse: false
  });

  // Process the PMO task directly
  const result = await researchTeamAgent.processTask(pmoTask);

  console.log(`✅ Research collaboration triggered successfully for PMO ${pmoData.pmoId}`);

  return {
    success: result.success,
    researchTaskId: result.taskId,
    researchPlanId: (result as any).researchSpecificOutput?.researchPlanId,
    output: result.output,
    documentIds: result.outputDocumentIds,
    methodology: (result as any).researchSpecificOutput?.researchMethodology,
    triggeredAt: new Date().toISOString(),
    pmoId: pmoData.pmoId,
    notificationId: pmoData.notificationId,
    message: 'Research collaboration triggered successfully',
    collaborationData: {
      source: 'Research Team Agent',
      pmoId: pmoData.pmoId,
      processedAt: new Date().toISOString(),
      teamId: AgenticTeamId.Research,
      methodology: (result as any).researchSpecificOutput?.researchMethodology,
      error: result.error
    }
  };
}


