'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Send, RefreshCw, AlertTriangle } from 'lucide-react';
import ChartGenerator from './ChartGenerator';
import { CHART_TYPES } from '../lib/tools/chart-tool';
// import { LlmProvider } from 'lib/tools/llm-tool';

interface ChartConfig {
  chartType: string;
  title: string;
  subtitle?: string;
  data: Array<Record<string, any>>;
  xAxis?: { dataKey?: string; label?: string };
  yAxis?: { dataKey?: string; label?: string };
  colors?: string[];
  legend?: boolean;
  tooltip?: boolean;
  grid?: boolean;
  explanation?: string;
  colorScheme?: 'sequential' | 'diverging' | 'categorical' | 'monochrome';
  useDistinctColors?: boolean;
  donut?: boolean;
  scatterName?: string;
  colorByValue?: boolean;
  barKeys?: string[];
  lineKeys?: string[];
  areaKeys?: string[];
  columns?: Array<{ key: string; label: string }>;
  pagination?: boolean;
  rowsPerPage?: number;
  nodes?: Array<{ id: string; label: string; [key: string]: any }>;
  edges?: Array<{ source: string; target: string; [key: string]: any }>;
  direction?: string;
  fitView?: boolean;
  colorScale?: string[];
  showValues?: boolean;
  zAxis?: { dataKey?: string; label?: string };
  colorKey?: string;
}

interface ChartGenerationResponse {
  success: boolean;
  chartConfig?: ChartConfig;
  error?: string;
}

/**
 * Chart Tool component for generating charts from natural language prompts
 */
export default function ChartTool() {
  const [prompt, setPrompt] = useState<string>('');
  const [chartType, setChartType] = useState<string>('');
  const [model, setModel] = useState<string>('gpt-4o');
  const [provider, setProvider] = useState<string>('openai');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [chartConfig, setChartConfig] = useState<ChartConfig | null>(null);

  /**
   * Handle chart generation
   */
  const handleGenerateChart = async (): Promise<void> => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setLoading(true);
    setError(null);

    // Use OpenAI Model 03-2025-04-16 for tables
    let selectedModel = model;
    let selectedProvider = provider;

    // If the chart type is 'table', use the OpenAI o3-2025-04-16 model
    if (chartType === CHART_TYPES.TABLE) {
      selectedModel = "o3-2025-04-16";
      selectedProvider = "openai";
      console.log("Using OpenAI Model 03-2025-04-16 for table generation");
    }

    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'chart',
          params: {
            prompt,
            chartType: chartType || undefined,
            model: selectedModel,
            provider: selectedProvider
          }
        }),
      });

      const data = await response.json() as ChartGenerationResponse;

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate chart');
      }

      if (!data.success) {
        throw new Error(data.error || 'Chart generation failed');
      }

      setChartConfig(data.chartConfig || null);
    } catch (error: any) {
      console.error('Error generating chart:', error);
      setError(error.message || 'Failed to generate chart');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="chart-tool">
      <div className="flex items-center mb-4">
        <BarChart className="text-blue-400 mr-2" size={24} />
        <h2 className="text-xl font-semibold text-white">Chart Tool</h2>
      </div>
      <p className="text-zinc-400 mb-6">Generate data visualizations from natural language descriptions</p>

      {/* Input form */}
      <div className="space-y-4 mb-6">
        {/* Prompt input */}
        <div>
          <label htmlFor="chart-prompt" className="block text-sm font-medium text-zinc-400 mb-1">
            Prompt
          </label>
          <textarea
            id="chart-prompt"
            className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe the chart you want to generate..."
            rows={3}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
          />
        </div>

        {/* Chart type selection */}
        <div>
          <label htmlFor="chart-type" className="block text-sm font-medium text-zinc-400 mb-1">
            Chart Type (Optional)
          </label>
          <select
            id="chart-type"
            className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={chartType}
            onChange={(e) => setChartType(e.target.value)}
          >
            <option value="">Auto-detect best chart type</option>
            {Object.entries(CHART_TYPES).map(([key, value]) => (
              <option key={key} value={value}>
                {key.charAt(0) + key.slice(1).toLowerCase()} Chart
              </option>
            ))}
          </select>
          {chartType === CHART_TYPES.TABLE && (
            <p className="mt-1 text-xs text-blue-400">
              Tables will be generated using OpenAI Model 03-2025-04-16 for optimal results.
            </p>
          )}
        </div>

        {/* Model selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="model" className="block text-sm font-medium text-zinc-400 mb-1">
              Model
            </label>
            <select
              id="model"
              className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={model}
              onChange={(e) => setModel(e.target.value)}
            >
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4.1-2025-04-14">gpt-4.1-2025-04-14</option>
              <option value="o3-2025-04-16">o3-2025-04-16</option>
              <option value="o1-mini-2024-09-12">o1-mini-2024-09-12</option>
              <option value="claude-3-7-sonnet">Claude 3.7 Sonnet</option>
              <option value="claude-3-5-sonnet">Claude 3.5 Sonnet</option>
              <option value="claude-3-opus">Claude 3 Opus</option>
              <option value="gemini-2.5-pro">Gemini 2.5 Pro Preview</option>
              <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
              <option value="gemini-2.5-flash">Gemini 2.0 Flash</option>
              <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
              <option value="llama-3.3-70b-versatile">Llama 3.3 70B Versatile</option>
              <option value="deepseek-r1-distill-llama-70b">DeepSeek Distill Llama 70B</option>
            </select>
          </div>

          <div>
            <label htmlFor="provider" className="block text-sm font-medium text-zinc-400 mb-1">
              Provider
            </label>
            <select
              id="provider"
              className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="claude">Claude</option>
              <option value="google">Google</option>
              <option value="groq">Groq</option>
            </select>
          </div>
        </div>

        {/* Generate button */}
        <button
          onClick={handleGenerateChart}
          disabled={loading || !prompt.trim()}
          className={`w-full flex items-center justify-center py-3 px-4 rounded-md text-white font-medium ${
            loading || !prompt.trim()
              ? 'bg-zinc-700 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {loading ? (
            <>
              <RefreshCw className="animate-spin mr-2" size={18} />
              Generating...
            </>
          ) : (
            <>
              <Send className="mr-2" size={18} />
              Generate Chart
            </>
          )}
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-900/30 border border-red-700 rounded-md">
          <div className="flex items-start">
            <AlertTriangle className="text-red-400 mr-2 mt-0.5 flex-shrink-0" size={16} />
            <p className="text-red-200 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Chart display */}
      <div className="chart-display">
        {chartConfig && <ChartGenerator chartConfig={chartConfig} />}
      </div>
    </div>
  );
}




