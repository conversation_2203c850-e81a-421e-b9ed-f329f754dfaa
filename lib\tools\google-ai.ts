/**
 * Google AI integration for the LLM tool
 */

// Import the Google Generative AI SDK
import { GoogleGenerativeAI } from "@google/generative-ai";

// Define interfaces for Google AI processing
export interface GoogleAIProcessingOptions {
  prompt: string;
  model?: string;
}

/**
 * Process content with Google AI
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use
 * @returns The generated content
 */
export async function processWithGoogleAI(options: GoogleAIProcessingOptions): Promise<string> {
  try {
    const { prompt, model = "gemini-1.5-pro" } = options;

    // Get the API key from environment variables
    const googleApiKey = process.env.GOOGLE_API_KEY ||
                        process.env.GOOGLE_GENAI_API_KEY ||
                        process.env.GEMINI_API_KEY ||
                        'AIzaSyAT3dpxtORQfimr57Gqtv1QgMXGxPpu4nE';

    console.log('Initializing Google AI with API key:', googleApiKey ? 'Key found' : 'No key found');

    // Initialize the Google AI SDK
    const genAI = new GoogleGenerativeAI(googleApiKey);
    console.log('Google AI initialized successfully');

    // Format model name to ensure compatibility (e.g., "Gemini 1.5 Pro" → "gemini-1.5-pro")
    let formattedModel = model.toLowerCase().replace(/\s+/g, '-');

    // Map UI-friendly model names to API model names if needed
    // Updated with the latest model names and fallbacks
    const modelMap: Record<string, string> = {
        'gemini-1.5-pro': 'gemini-1.5-pro-latest',
        'gemini-1.5-pro-latest': 'gemini-1.5-pro-latest',
        'gemini-1.5-flash': 'gemini-1.5-flash-latest',
        'gemini-1.5-flash-latest': 'gemini-1.5-flash-latest',
        'gemini-2.5-flash': 'gemini-2.5-flash',
        'gemini-2.5-pro': 'gemini-2.5-pro'
    };

    if (modelMap[formattedModel]) {
        formattedModel = modelMap[formattedModel];
    }
    console.log(`Using Google AI model: ${formattedModel}`);

    // Get the gemini model instance
    const geminiModel = genAI.getGenerativeModel({ model: formattedModel });
    console.log('Gemini model instance created');

    // Generate content with proper error handling
    console.log('Sending prompt to Gemini:', prompt);
    const result = await geminiModel.generateContent(prompt);
    console.log('Received result from Gemini');

    // Extract text from response based on Gemini API structure
    if (result && result.response) {
        const responseText = result.response.text();
        console.log('Extracted text from Gemini response');
        return responseText;
    } else {
        console.error("Unexpected Gemini API response format");
        return "Error: Unexpected response format from Gemini API";
    }
  } catch (error: any) {
    console.error("Error processing content with Google AI:", error);

    // Check for specific error types to provide better fallback behavior
    const errorMessage = error.message || "Unknown error";

    // If it's a 404 error (model not found) or any other error, use the fallback chain
    if (errorMessage) {
      console.log("Error detected with Gemini model. Initiating fallback chain...");

      // We'll return a specific error message that will trigger the fallback chain in the calling code
      // The actual fallback to OpenAI, Claude, and DeepSeek will be handled at a higher level
      return `FALLBACK_REQUIRED: ${errorMessage}`;
    }

    return `Error from Google AI: ${errorMessage}`;
  }
}

/**
 * Get available Google AI models
 * @returns List of available models
 */
export function getGoogleAIModels(): string[] {
  return [
    "gemini-2.5-pro",         // Primary model
    "gemini-1.5-pro-latest",  // Fallback option
    "gemini-1.5-flash-latest", // Additional fallback
    "gemini-1.5-pro",  // Legacy format
    "gemini-1.5-flash", // Legacy format
    "gemini-2.5-flash",
  ];
}
