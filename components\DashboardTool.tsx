'use client';

import React, { useState } from 'react';
import { LayoutDashboard, Send, RefreshCw, AlertTriangle } from 'lucide-react';
import Dashboard from './Dashboard';
import { DASHBOARD_LAYOUTS, DashboardLayout } from '../lib/tools/dashboard-tool';
import { LlmProvider } from '../lib/tools/llm-tool';

// Using DashboardConfig from dashboard-tool.ts

// Define a separate interface for the API response to avoid type conflicts
interface ApiDashboardConfig {
  title: string;
  subtitle?: string;
  layout?: string;
  insights?: string;
  recommendations?: string;
  visualizations?: Array<{
    id: string;
    chartType: string;
    title: string;
    subtitle?: string;
    width?: number;
    height?: number;
    data: any[];
    colors?: string[];
    legend?: boolean;
    tooltip?: boolean;
    grid?: boolean;
    explanation?: string;
    featured?: boolean;
    [key: string]: any;
  }>;
}

interface DashboardResponse {
  success: boolean;
  dashboardConfig?: ApiDashboardConfig;
  error?: string;
}

/**
 * Dashboard Tool component for generating dashboards from natural language prompts
 */
const DashboardTool: React.FC = () => {
  const [prompt, setPrompt] = useState<string>('');
  const [layout, setLayout] = useState<string>('');
  const [model, setModel] = useState<string>('gpt-4o');
  const [provider, setProvider] = useState<LlmProvider>('openai');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [dashboardConfig, setDashboardConfig] = useState<ApiDashboardConfig | null>(null);

  /**
   * Handle dashboard generation
   */
  const handleGenerateDashboard = async (): Promise<void> => {
    if (!prompt.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'dashboard',
          params: {
            prompt,
            layout: layout || undefined,
            model,
            provider
          }
        }),
      });

      const data = await response.json() as DashboardResponse;

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate dashboard');
      }

      if (!data.success) {
        throw new Error(data.error || 'Dashboard generation failed');
      }

      setDashboardConfig(data.dashboardConfig || null);
    } catch (error) {
      console.error('Error generating dashboard:', error);
      setError((error as Error).message || 'Failed to generate dashboard');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Input form */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 p-6 space-y-4">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <LayoutDashboard className="mr-2 text-blue-400" size={20} />
          Dashboard Generator
        </h2>
        <p className="text-zinc-400 text-sm">
          Generate interactive dashboards with multiple visualizations from natural language descriptions.
        </p>

        {/* Prompt input */}
        <div>
          <label htmlFor="dashboard-prompt" className="block text-sm font-medium text-zinc-400 mb-1">
            Dashboard Description
          </label>
          <textarea
            id="dashboard-prompt"
            rows={4}
            className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe the dashboard you want to create, including the types of data and insights needed..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
          />
        </div>

        {/* Layout selection */}
        <div>
          <label htmlFor="dashboard-layout" className="block text-sm font-medium text-zinc-400 mb-1">
            Layout (Optional)
          </label>
          <select
            id="dashboard-layout"
            className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={layout}
            onChange={(e) => setLayout(e.target.value as DashboardLayout | '')}
          >
            <option value="">Auto-detect best layout</option>
            {Object.entries(DASHBOARD_LAYOUTS).map(([key, value]) => (
              <option key={key} value={value as string}>
                {key.charAt(0) + key.slice(1).toLowerCase()} Layout
              </option>
            ))}
          </select>
        </div>

        {/* Model selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="model" className="block text-sm font-medium text-zinc-400 mb-1">
              Model
            </label>
            <select
              id="model"
              className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={model}
              onChange={(e) => setModel(e.target.value)}
            >
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4.1-2025-04-14">gpt-4.1-2025-04-14</option>
              <option value="o3-2025-04-16">o3-2025-04-16</option>
              <option value="o1-mini-2024-09-12">o1-mini-2024-09-12</option>
              <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
              <option value="claude-opus-4-0">Claude Opus 4.0</option>
              <option value="claude-3-7-sonnet">Claude 3.7 Sonnet</option>
              <option value="claude-3-5-sonnet">Claude 3.5 Sonnet</option>
              <option value="claude-3-opus">Claude 3 Opus</option>
              <option value="gemini-2.5-pro">Gemini 2.5 Pro Preview</option>
              <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
              <option value="gemini-2.5-flash">Gemini 2.0 Flash</option>
              <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
              <option value="llama-3.3-70b-versatile">Llama 3.3 70B Versatile</option>
              <option value="deepseek-r1-distill-llama-70b">DeepSeek Distill Llama 70B</option>
            </select>
          </div>

          <div>
            <label htmlFor="provider" className="block text-sm font-medium text-zinc-400 mb-1">
              Provider
            </label>
            <select
              id="provider"
              className="w-full p-3 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={provider}
              onChange={(e) => setProvider(e.target.value as LlmProvider)}
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="claude">Claude</option>
              <option value="google">Google</option>
              <option value="groq">Groq</option>
            </select>
          </div>
        </div>

        {/* Generate button */}
        <button
          onClick={handleGenerateDashboard}
          disabled={loading || !prompt.trim()}
          className={`w-full flex items-center justify-center py-3 px-4 rounded-md text-white font-medium ${
            loading || !prompt.trim()
              ? 'bg-zinc-700 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {loading ? (
            <>
              <RefreshCw className="animate-spin mr-2" size={18} />
              Generating...
            </>
          ) : (
            <>
              <Send className="mr-2" size={18} />
              Generate Dashboard
            </>
          )}
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-900/30 border border-red-700 rounded-md">
          <div className="flex items-start">
            <AlertTriangle className="text-red-400 mr-2 mt-0.5 flex-shrink-0" size={16} />
            <p className="text-red-200 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Dashboard output */}
      {dashboardConfig && (
        <Dashboard dashboardConfig={{
          title: dashboardConfig.title,
          subtitle: dashboardConfig.subtitle,
          layout: dashboardConfig.layout || 'grid',
          insights: dashboardConfig.insights,
          recommendations: dashboardConfig.recommendations,
          visualizations: (dashboardConfig.visualizations || []).map(viz => ({
            id: viz.id,
            chartType: viz.chartType,
            title: viz.title,
            subtitle: viz.subtitle,
            width: viz.width || 1,
            height: viz.height || 300,
            data: viz.data || [],
            colors: viz.colors,
            legend: viz.legend,
            tooltip: viz.tooltip,
            grid: viz.grid,
            explanation: viz.explanation,
            featured: viz.featured
          }))
        }} />
      )}
    </div>
  );
};

export default DashboardTool;