import { processWithGroq } from '../tools/groq-ai';
import { adminDb } from '../../components/firebase-admin';
import { addTask } from '../../app/lib/firebase/planner';
import { Task, TaskPriority, TaskStatus } from '../../admin/planner/types';
import { CalendarTool } from '../tools/calendarTool';
import { z } from 'zod';
import {
  getHierarchicalPMOData,
  addTaskToProject,
  createProjectDocument,
  getProjectTasks,
  getProjectTaskIdsForEnhancedNotes,
  HierarchicalPMOData
} from '../firebase/pmoHierarchical';

export interface PMOProjectsTaskAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: PMOTaskStreamUpdate) => void;
}

export interface PMOTaskStreamUpdate {
  stage: 'retrieving-output' | 'extracting-tasks' | 'creating-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface PMOProjectsTaskAgentResult {
  success: boolean;
  tasksCreated: Array<{
    taskId: string;
    title: string;
    projectId: string;
    description: string;
  }>;
  error?: string;
  analysis?: string;
  totalTasksExtracted: number;
}

// Schema for extracted task data
const ExtractedTaskSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: z.string(),
  priority: z.enum(['Low', 'Medium', 'High']).default('High'),
  estimatedDuration: z.string().optional(), // e.g., "3 days", "1 week"
  dependencies: z.array(z.string()).optional(),
  notes: z.string().optional(),
  dueDate: z.string().optional(), // YYYY-MM-DD format
  startDate: z.string().optional(), // YYYY-MM-DD format
  assignedTo: z.array(z.string()).optional(), // Team assignments from Strategic Analysis
  teamAssignment: z.string().optional() // Original team name from Strategic Analysis
});

const ExtractedTasksResponseSchema = z.object({
  tasks: z.array(ExtractedTaskSchema),
  analysis: z.string(),
  confidence: z.number().min(0).max(1),
  projectContext: z.string()
});

type ExtractedTask = z.infer<typeof ExtractedTaskSchema>;
type ExtractedTasksResponse = z.infer<typeof ExtractedTasksResponseSchema>;

export class PMOProjectsTaskAgent {
  private options: PMOProjectsTaskAgentOptions;
  private calendarTool: CalendarTool;

  constructor(options: PMOProjectsTaskAgentOptions = {}) {
    this.options = {
      includeExplanation: true,
      streamResponse: false,
      ...options
    };
    this.calendarTool = new CalendarTool();
  }

  // Removed the first duplicate implementation of extractTasksFromAgentOutput

  /**
   * Extract tasks from team-specific agent output (for preview - does NOT create tasks)
   * Routes to correct agent based on PMO team assignment:
   * - Research PMO records → ResearchAgentManager output
   * - Marketing PMO records → Strategic Director Agent output
   */
  async extractTasksFromAgentOutput(
    requestId: string,
    projectName: string,
    projectDescription: string
  ): Promise<{
    success: boolean;
    tasksCreated: Array<{
      title: string;
      description: string;
      category: string;
      priority: TaskPriority; // Changed from 'High' | 'Medium' | 'Low'
      dueDate: Date;
      startDate: Date;
      estimatedDuration?: string;
      dependencies?: string[];
      notes?: string;
      assignedTo?: string[]; // Team assignments from Strategic Analysis
      teamAssignment?: string; // Original team name from Strategic Analysis
      subtasks?: Array<{
        title: string;
        description: string;
        priority: TaskPriority; // Changed from 'High' | 'Medium' | 'Low'
        dueDate?: Date;
        estimatedDuration?: string;
      }>;
    }>;
    error?: string;
    analysis?: string;
    agentOutputId?: string; // Include agent output ID for reference
  }> {
    try {
      console.log(`PMOProjectsTaskAgent: Extracting tasks for preview from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output and determine team assignment
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Determine the correct agent type based on PMO metadata
      const agentType = agentOutput.agentType;
      const teamAssignment = this._determineTeamAssignment(agentOutput);

      console.log(`PMOProjectsTaskAgent: Agent type: ${agentType}, Team assignment: ${teamAssignment}, PMO ID: ${agentOutput.pmoMetadata?.pmoId || agentOutput.metadata?.pmoId || 'unknown'}`);

      // Step 3: Validate agent type matches team assignment
      const expectedAgentType = this._getExpectedAgentType(teamAssignment);
      if (expectedAgentType && agentType !== expectedAgentType) {
        console.warn(`PMOProjectsTaskAgent: Agent type mismatch - Team: ${teamAssignment}, Expected: ${expectedAgentType}, Actual: ${agentType}`);
        // Continue processing but log the mismatch for debugging
      }

      // Step 4: Extract tasks using team-specific analysis
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis,
          agentOutputId: requestId
        };
      }

      // Step 3: Format tasks for preview (with calculated dates and subtasks)
      const formattedTasks = [];
      for (const task of extractedTasks.tasks) {
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        // Subtasks are not currently generated - this is a placeholder for future enhancement
        const subtasks: Array<{
          title: string;
          description: string;
          priority: TaskPriority;
          dueDate?: Date;
          estimatedDuration?: string;
        }> = [];

        // Preserve team names instead of converting to user emails for consistency with preview
        const teamAssignment = task.teamAssignment || 'ADMIN';
        const preservedTeamAssignment = [teamAssignment];

        console.log(`PMOProjectsTaskAgent: Task "${task.title}" - Team: ${task.teamAssignment}, Preserved Assignment: [${preservedTeamAssignment.join(', ')}]`);

        formattedTasks.push({
          title: task.title,
          description: task.description,
          category: task.category,
          priority: 'High' as TaskPriority, // Consistent with class description & TaskPriority type
          dueDate,
          startDate,
          estimatedDuration: task.estimatedDuration,
          dependencies: task.dependencies,
          notes: task.notes,
          assignedTo: preservedTeamAssignment, // Preserve team names as shown in preview
          teamAssignment: task.teamAssignment,
          subtasks
        });
      }

      return {
        success: true,
        tasksCreated: formattedTasks,
        analysis: extractedTasks.analysis,
        agentOutputId: requestId
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks for preview:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error),
        agentOutputId: requestId
      };
    }
  }

  /**
   * Create tasks for a project from team-specific agent output
   * Routes to correct agent based on PMO team assignment:
   * - Research PMO records → ResearchAgentManager output
   * - Marketing PMO records → Strategic Director Agent output
   */
  async createTasksFromAgentOutput(
    requestId: string,
    projectId: string,
    projectName: string,
    projectDescription: string
  ): Promise<PMOProjectsTaskAgentResult> {
    try {
      console.log(`PMOProjectsTaskAgent: Starting task creation for project ${projectId} from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output and determine team assignment
      this._streamUpdate('retrieving-output', null, 'Retrieving agent output from Firebase...');
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 1.5: Determine the correct agent type based on PMO metadata
      const agentType = agentOutput.agentType;
      const teamAssignment = this._determineTeamAssignment(agentOutput);

      console.log(`PMOProjectsTaskAgent: Agent type: ${agentType}, Team assignment: ${teamAssignment}, PMO ID: ${agentOutput.pmoMetadata?.pmoId || agentOutput.metadata?.pmoId || 'unknown'}`);

      // Validate agent type matches team assignment
      const expectedAgentType = this._getExpectedAgentType(teamAssignment);
      if (expectedAgentType && agentType !== expectedAgentType) {
        console.warn(`PMOProjectsTaskAgent: Agent type mismatch - Team: ${teamAssignment}, Expected: ${expectedAgentType}, Actual: ${agentType}`);
        // Continue processing but log the mismatch for debugging
      }

      // Step 2: Extract tasks using Groq deepseek LLM
      this._streamUpdate('extracting-tasks', null, 'Extracting tasks using Groq deepseek LLM...');
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis,
          totalTasksExtracted: 0
        };
      }

      // Step 3: Create tasks in Firebase
      this._streamUpdate('creating-tasks', null, `Creating ${extractedTasks.tasks.length} tasks...`);
      const createdTasks = await this._createTasks(extractedTasks.tasks, projectId, agentOutput);

      this._streamUpdate('complete', null, 'Task creation completed successfully');

      return {
        success: true,
        tasksCreated: createdTasks,
        analysis: extractedTasks.analysis,
        totalTasksExtracted: extractedTasks.tasks.length
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error creating tasks:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error),
        totalTasksExtracted: 0
      };
    }
  }

  /**
   * Retrieve agent output from Firestore with enhanced data structure validation
   */
  private async _getAgentOutput(requestId: string, expectedAgentType?: string): Promise<any> {
    try {
      const doc = await adminDb.collection('Agent_Output').doc(requestId).get();

      if (!doc.exists) {
        throw new Error(`Agent output document not found: ${requestId}`);
      }

      const agentOutput = doc.data();
      const actualAgentType = agentOutput?.agentType;

      // Enhanced logging for data structure validation
      console.log(`PMOProjectsTaskAgent: Retrieved agent output - requestId: ${requestId}, agentType: ${actualAgentType}, expected: ${expectedAgentType || 'any'}`);

      // ENHANCED: Validate data structure and log key fields
      console.log(`PMOProjectsTaskAgent: Data structure validation:`);
      console.log(`  - result.output exists: ${!!(agentOutput?.result?.output)}`);
      console.log(`  - result.output length: ${agentOutput?.result?.output?.length || 0}`);
      console.log(`  - result.thinking exists: ${!!(agentOutput?.result?.thinking)}`);
      console.log(`  - category: ${agentOutput?.category || 'not found'}`);
      console.log(`  - pmoMetadata.category: ${agentOutput?.pmoMetadata?.category || 'not found'}`);
      console.log(`  - metadata.category: ${agentOutput?.metadata?.category || 'not found'}`);

      // Validate that we have the essential result.output field
      if (!agentOutput?.result?.output) {
        console.warn(`PMOProjectsTaskAgent: WARNING - No result.output field found in agent output ${requestId}`);
        console.log(`PMOProjectsTaskAgent: Available fields:`, Object.keys(agentOutput || {}));
        console.log(`PMOProjectsTaskAgent: Result object:`, agentOutput?.result ? Object.keys(agentOutput.result) : 'No result object');
      }

      // If expected agent type is specified, validate it matches
      if (expectedAgentType && actualAgentType !== expectedAgentType) {
        console.warn(`PMOProjectsTaskAgent: Agent type mismatch - expected: ${expectedAgentType}, actual: ${actualAgentType}`);
        // Don't throw error, just log warning for now to maintain backward compatibility
      }

      return agentOutput;
    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error retrieving agent output:', error);
      throw error;
    }
  }

  /**
   * Extract tasks from agent output using Groq deepseek LLM
   */
  private async _extractTasksWithGroq(
    agentOutput: any,
    projectName: string,
    projectDescription: string
  ): Promise<ExtractedTasksResponse> {
    try {
      // ENHANCED DATA ACCESS: Properly access the result.output field which contains the strategic analysis
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';

      // FIXED: Correct category access pattern - check multiple possible locations
      const category = agentOutput.category ||
                      agentOutput.pmoMetadata?.category ||
                      agentOutput.metadata?.category ||
                      '';

      // VALIDATION: Ensure we have the strategic analysis content
      if (!outputContent || outputContent.trim().length === 0) {
        console.warn(`PMOProjectsTaskAgent: No strategic analysis content found in result.output for agentType: ${agentType}`);
        throw new Error('No strategic analysis content found in result.output field');
      }

      console.log(`PMOProjectsTaskAgent: Processing strategic analysis content - Length: ${outputContent.length} chars, Agent Type: ${agentType}, Category: ${category}`);

      // ENHANCED: Analyze strategic content structure for better task extraction
      const contentAnalysis = this._analyzeStrategicContent(outputContent);
      console.log(`PMOProjectsTaskAgent: Strategic content analysis:`, contentAnalysis);

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'dd-MM-yyyy HH:mm:ss'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      // SIMPLIFIED: Check for PMO flag in metadata or agent output structure
      const isPMOContext = !!(agentOutput.pmoMetadata?.pmoId ||
                             agentOutput.metadata?.pmoId ||
                             agentOutput.metadata?.source === 'PMO' ||
                             agentType.toLowerCase().includes('strategic') ||
                             agentType === 'ResearchAgentManager');

      console.log(`PMOProjectsTaskAgent: Task extraction mode - PMO Context: ${isPMOContext}, Agent Type: ${agentType}, Category: ${category}`);
      console.log(`PMOProjectsTaskAgent: Strategic analysis content preview: ${outputContent.substring(0, 200)}...`);

      const prompt = `
You are an advanced Task Extraction AI specialized in analyzing comprehensive Strategic Analysis documents to extract detailed, actionable tasks for project implementation.

**CURRENT DATE:** ${todayDate}

CONTEXT:
Project Name: ${projectName}
Project Description: ${projectDescription}
Agent Type: ${agentType}
Category: ${category}
PMO Context: ${isPMOContext ? 'YES - Enhanced PMO task extraction mode' : 'NO'}
Strategic Analysis Content Length: ${outputContent.length} characters

STRATEGIC ANALYSIS DOCUMENT TO ANALYZE:
=== STRATEGIC ANALYSIS CONTENT ===
${outputContent}

=== STRATEGIC THINKING PROCESS ===
${thinkingContent}

**CRITICAL: COMPREHENSIVE STRATEGIC ANALYSIS EXTRACTION**
The strategic analysis document above contains rich, detailed information that MUST be extracted into actionable tasks:

🎯 **PRIMARY EXTRACTION TARGETS:**
- **Specific Recommendations**: Extract exact recommendations and action items mentioned
- **Detailed Methodologies**: Include specific approaches, frameworks, and processes described
- **Success Criteria & KPIs**: Extract measurable outcomes and performance indicators
- **Resource Requirements**: Include tools, personnel, and budget considerations mentioned
- **Timeline & Milestones**: Extract specific deadlines, phases, and scheduling details
- **Dependencies & Prerequisites**: Identify task sequencing and requirements
- **Risk Factors**: Include risk assessments and mitigation strategies
- **Deliverables**: Extract specific outputs, reports, and artifacts mentioned
- **Quality Standards**: Include acceptance criteria and review processes
- **Stakeholder Requirements**: Extract audience considerations and requirements

🔍 **STRATEGIC CONTENT MINING INSTRUCTIONS:**
1. **READ THE ENTIRE STRATEGIC ANALYSIS** - Don't just scan, thoroughly analyze the content
2. **EXTRACT SPECIFIC DETAILS** - Use exact terminology, metrics, and specifications from the analysis
3. **PRESERVE STRATEGIC CONTEXT** - Maintain the strategic reasoning and business rationale
4. **IDENTIFY IMPLEMENTATION PHASES** - Extract logical workflow and sequencing
5. **CAPTURE TEAM ASSIGNMENTS** - Preserve exact team names and role assignments
6. **INCLUDE SUCCESS METRICS** - Extract specific KPIs and measurement criteria

Your task is to transform this strategic analysis into detailed, actionable tasks that preserve the strategic intelligence and specific recommendations contained within.

${isPMOContext ? `
**PMO TASK EXTRACTION MODE ACTIVATED**
CRITICAL INSTRUCTIONS FOR PMO CONTENT:

🎯 **PRIMARY FOCUS: TEAM ASSIGNMENTS SECTION**
1. **FIRST PRIORITY**: Search for sections titled "TEAM ASSIGNMENTS", "4. TEAM ASSIGNMENTS", "ASSIGNMENTS", or similar
2. **EXACT EXTRACTION**: Extract tasks EXACTLY as they appear in the TEAM ASSIGNMENTS section
3. **PRESERVE FORMAT**: Maintain the exact task titles, descriptions, and team assignments from the Strategic Analysis
4. **TEAM MAPPING**: Look for patterns like:
   - "Task 1 – [Task Title] • Assigned to : [Team Name]"
   - "**Task**: [Description] **Assigned to**: [Team Name]"
   - "• [Task Description] (Research Team)"
   - "[Task Number] – [Task Title] • Assigned to : [Team Name] • Rationale : [Reason]"

🔍 **SECTION SCANNING PRIORITY ORDER**:
1. "TEAM ASSIGNMENTS" or "4. TEAM ASSIGNMENTS" (HIGHEST PRIORITY)
2. "ASSIGNMENTS (all deadlines are from campaign Day 0)"
3. "Task 1 –", "Task 2 –", "Task 3 –" numbered lists
4. Sections with "Assigned to:" patterns
5. "Strategic Reasoning", "Implementation", "Action Items" (LOWER PRIORITY)

📋 **EXTRACTION RULES**:
- If TEAM ASSIGNMENTS section exists, extract ALL tasks from it first
- Preserve team names EXACTLY: "Research Team", "Marketing Team", "Software Design Team", etc.
- Keep task numbering and structure from the original
- Include rationale and dependencies if mentioned
- Only create additional tasks if TEAM ASSIGNMENTS section is incomplete or missing
- DO NOT modify or rephrase tasks that are already well-defined in TEAM ASSIGNMENTS

🚨 **CRITICAL**: The TEAM ASSIGNMENTS section contains the authoritative task list. Extract these tasks with 100% fidelity.
` : ''}

**ENHANCED TASK EXTRACTION REQUIREMENTS:**

🎯 **STRATEGIC CONTENT EXTRACTION (MANDATORY):**
1. **MINE SPECIFIC RECOMMENDATIONS**: Extract exact recommendations, methodologies, and strategic approaches mentioned in the analysis
2. **PRESERVE STRATEGIC INSIGHTS**: Include specific insights, data points, market analysis, and strategic reasoning in task descriptions
3. **CAPTURE DETAILED DELIVERABLES**: Extract specific deliverables, reports, frameworks, documents, and outputs with their exact specifications
4. **INCLUDE SUCCESS METRICS**: Extract KPIs, success criteria, measurement approaches, and performance indicators specified in the analysis
5. **IDENTIFY DEPENDENCIES**: Extract specific dependencies, prerequisites, sequencing, and workflow mentioned in the strategic plan
6. **RESOURCE REQUIREMENTS**: Include specific resource needs, tools, technologies, personnel, and budget considerations identified
7. **PRESERVE TEAM ASSIGNMENTS**: Maintain exact team assignments from the Strategic Analysis (e.g., "Research Team", "Marketing Team", "Software Design Team")
8. **TIMELINE EXTRACTION**: Use specific timelines, milestones, deadlines, and phasing mentioned in the strategic analysis
9. **RISK CONSIDERATIONS**: Include risk factors, mitigation strategies, and contingency plans mentioned in the strategic content
10. **QUALITY STANDARDS**: Extract specific quality criteria, review processes, acceptance standards, and validation approaches

📋 **STRATEGIC ANALYSIS PROCESSING:**
${isPMOContext ? `
**PMO STRATEGIC ANALYSIS MODE:**
11. **EXTRACT ALL STRATEGIC TASKS**: Mine the strategic analysis for ALL mentioned tasks, action items, and implementation steps
12. **PRESERVE STRATEGIC CONTEXT**: Maintain the strategic reasoning and business rationale for each task
13. **DETAILED IMPLEMENTATION**: Convert high-level strategic recommendations into detailed, actionable implementation tasks
14. **COMPREHENSIVE COVERAGE**: Ensure all aspects of the strategic analysis are represented in the task extraction
` : `
**STANDARD STRATEGIC ANALYSIS MODE:**
11. **COMPREHENSIVE EXTRACTION**: Extract 5-12 detailed tasks that represent the full scope of the strategic analysis
12. **STRATEGIC DEPTH**: Ensure each task reflects the strategic depth and analytical rigor of the source document
`}

**DETAIL EXTRACTION FOCUS AREAS:**
- Look for sections with specific recommendations and action items
- Extract methodology details and implementation approaches
- Capture specific deliverables and their requirements
- Include success criteria and measurement approaches
- Identify stakeholder requirements and considerations
- Extract timeline and milestone information
- Include resource and tool requirements
- Capture risk factors and mitigation strategies

**CRITICAL: JSON FORMATTING REQUIREMENTS**
You MUST return ONLY a valid JSON object. Do not include any text before or after the JSON.
Do not use markdown code blocks. Do not add explanatory text outside the JSON.
Ensure proper JSON syntax: use double quotes, no trailing commas, properly closed brackets.

**ENHANCED RESPONSE FORMAT:** Return a valid JSON object with this exact structure:
{
  "tasks": [
    {
      "title": "Specific, actionable task title extracted directly from strategic analysis (max 80 chars)",
      "description": "COMPREHENSIVE description extracted from strategic analysis including: 1) EXACT deliverables and specifications mentioned in the analysis, 2) SUCCESS CRITERIA and KPIs directly from the strategic content, 3) DETAILED METHODOLOGY or approach specified in the analysis, 4) RESOURCE REQUIREMENTS identified in the strategic plan, 5) QUALITY STANDARDS and acceptance criteria from the analysis, 6) RISK CONSIDERATIONS and mitigation steps mentioned, 7) STRATEGIC CONTEXT and business rationale, 8) IMPLEMENTATION DETAILS and specific steps outlined. CRITICAL: Extract rich detail from the strategic analysis content - do NOT create generic descriptions.",
      "category": "Research & Analysis|Content Creation|Strategy Development|Implementation|Quality Assurance",
      "priority": "High",
      "estimatedDuration": "X days/weeks (extract from strategic timeline recommendations or estimate based on detailed scope)",
      "dependencies": ["Specific task dependencies identified in the strategic analysis"],
      "notes": "STRATEGIC CONTEXT extracted from analysis: implementation notes, resource requirements, success metrics, risk factors, stakeholder considerations, strategic rationale, and any special requirements mentioned in the strategic analysis document",
      "dueDate": "YYYY-MM-DD (based on strategic timeline or realistic deadline from ${todayDate})",
      "startDate": "YYYY-MM-DD (use ${todayDate} or logical start date from strategic plan)",
      "teamAssignment": "EXACT team name from Strategic Analysis (e.g., 'Research Team', 'Marketing Team', 'Software Design Team')",
      "assignedTo": []
    }
  ],
  "analysis": "DETAILED explanation of how tasks were extracted from the strategic analysis, what specific sections were prioritized, which strategic recommendations were converted to tasks, and how the strategic intelligence was preserved in the task extraction process",
  "confidence": 0.85,
  "projectContext": "COMPREHENSIVE summary of the strategic context, key insights, strategic recommendations, and business rationale that influenced task extraction from the strategic analysis document"
}

ENHANCED TASK CATEGORIZATION SYSTEM:
Use these standardized categories based on task content and team assignments:

📊 **"Research & Analysis"** - For data gathering, market research, competitive analysis tasks
   - Keywords: research, analysis, data, study, investigate, survey, metrics, insights
   - Team patterns: Research Team → Research & Analysis
   - Examples: "Conduct market research", "Analyze competitor data", "Validate metrics"

   **"Software Development"** - For software development, UI/UX design, technical implementation tasks
   - Keywords: develop, design, implement, build, create (technical), system, software, technical
   - Team patterns: Software Design Team → Software Development
   - Examples: "Develop software solution", "Design user interface", "Implement technical features"

   **"Technical Documentation"** - For documentation, reporting, and technical writing tasks
   - Keywords: document, report, write (technical), documentation, technical writing
   - Team patterns: Software Design Team → Technical Documentation
   - Examples: "Write technical documentation", "Create system report", "Develop user manual"

   ✍️ **"Content Creation"** - For messaging, documentation, creative asset development
   - Keywords: create, write, develop content, messaging, documentation, assets, copy
   - Team patterns: Marketing Team (content tasks) → Content Creation
   - Examples: "Create messaging framework", "Develop content calendar", "Write documentation"

🎯 **"Strategy Development"** - For planning, framework creation, strategic analysis
   - Keywords: strategy, plan, framework, roadmap, approach, methodology, design (strategic)
   - Team patterns: Strategic tasks → Strategy Development
   - Examples: "Develop strategic plan", "Create implementation framework", "Design approach"

⚙️ **"Implementation"** - For execution, deployment, operational tasks
   - Keywords: implement, execute, deploy, launch, build, develop (technical), setup
   - Team patterns: Software Design Team, Sales Team → Implementation
   - Examples: "Implement system", "Deploy solution", "Execute campaign", "Build features"

✅ **"Quality Assurance"** - For review, testing, validation tasks
   - Keywords: review, test, validate, verify, audit, check, assess, evaluate
   - Team patterns: QA tasks, review tasks → Quality Assurance
   - Examples: "Review deliverables", "Test implementation", "Validate results"

TASK EXTRACTION GUIDELINES:
${isPMOContext ? `
**PMO-SPECIFIC GUIDELINES:**
- **PRIORITY 1**: Extract ALL tasks from TEAM ASSIGNMENTS section (typically 3-8 tasks)
- **PRIORITY 2**: If TEAM ASSIGNMENTS incomplete, extract additional tasks to reach 8-15 total
- **PRESERVE EXACTLY**: Task titles, descriptions, and team assignments from TEAM ASSIGNMENTS
- **TEAM FIDELITY**: Maintain exact team names ("Research Team", "Marketing Team", etc.)
- **STRUCTURE**: Keep original task numbering and dependencies
- **PHASES**: Include tasks from Planning, Research, Development, Implementation, Testing, Launch, Evaluation
- **DELIVERABLES**: Pay attention to specific deliverables mentioned in the strategic analysis
- **BREAKDOWN**: Only break down complex items if TEAM ASSIGNMENTS section lacks detail
- **COMPLETENESS**: Ensure all tasks from TEAM ASSIGNMENTS are captured before adding others
- **SMART CATEGORIZATION**: Apply the enhanced categorization system based on task content and team assignments
` : `
**GENERAL GUIDELINES:**
- Extract 5-12 specific tasks (avoid too many or too few)
- **SMART CATEGORIZATION**: Apply the enhanced categorization system based on task content
`}
- Task titles should be action-oriented (start with verbs like "Create", "Develop", "Analyze", "Implement")
- Descriptions should include specific deliverables and success criteria
- Categories should match the project's domain and strategic focus
- Dependencies should reference other task titles in the same extraction
- Start dates should be TODAY (${todayDate}) or logical future dates within 1-7 days
- Due dates should be realistic based on task complexity and dependencies (typically 3-30 days from start)
- Use YYYY-MM-DD format for all dates and consider weekends and realistic timelines
- Create a logical workflow sequence with proper task dependencies
- All tasks get HIGH priority as specified in requirements

${isPMOContext ? `
**PMO TASK EXAMPLES FROM TEAM ASSIGNMENTS SECTION:**
EXTRACT EXACTLY AS SHOWN:
- "Task 1 – Validate X, Y, Z Metrics • Assigned to : Research Team"
- "Task 2 – Competitive Analysis Report • Assigned to : Research Team"
- "Task 3 – Messaging Framework & Personas • Assigned to : Marketing Team"
- "Conduct comprehensive market research and competitor analysis (Research Team)"
- "Develop brand messaging framework and value proposition (Marketing Team)"
- "Create content strategy and editorial calendar (Marketing Team)"
- "Design and develop marketing website landing pages (Software Design Team)"

**PRESERVE TEAM ASSIGNMENTS EXACTLY:**
- Research Team → "Research Team"
- Marketing Team → "Marketing Team"
- Software Design Team → "Software Design Team"
- Sales Team → "Sales Team"
- Business Analysis Team → "Business Analysis Team"
` : `
**GENERAL TASK EXAMPLES:**
- "Create brand messaging framework document"
- "Develop social media content calendar for Q1"
- "Conduct competitor analysis and create comparison matrix"
- "Design landing page wireframes and mockups"
- "Implement email marketing automation workflow"
`}

If no clear tasks can be extracted, return an empty tasks array with appropriate analysis.

FINAL REMINDER: Return ONLY the JSON object. No additional text, no markdown formatting, no explanations outside the JSON structure.
`;

      // Use Gemini for PMO contexts for better reasoning, Groq for others
      let response: string;
      if (isPMOContext) {
        console.log('PMOProjectsTaskAgent: Using Gemini for enhanced PMO task extraction');
        const { processWithGoogleAI } = await import('../../lib/tools/google-ai');
        response = await processWithGoogleAI({
          prompt,
          model: 'gemini-2.5-pro'
        });
      } else {
        response = await processWithGroq({
          prompt,
          model: 'deepseek-r1-distill-llama-70b',
          modelOptions: {
            temperature: 0.3,
            max_tokens: 4000,
          },
        });
      }

      // Parse and validate the response using robust Zod schema validation
      console.log(`PMOProjectsTaskAgent: Raw response preview: ${response.substring(0, 500)}...`);

      const validatedResponse = this._parseAndValidateResponse(response, isPMOContext);

      // Enhance task categorization with intelligent mapping
      const enhancedTasks = validatedResponse.tasks.map(task => ({
        ...task,
        category: this._enhanceTaskCategory(task)
      }));

      const enhancedResponse = {
        ...validatedResponse,
        tasks: enhancedTasks
      };

      console.log(`PMOProjectsTaskAgent: Successfully extracted ${enhancedResponse.tasks.length} tasks using ${isPMOContext ? 'Gemini (PMO mode)' : 'Groq (standard mode)'}`);
      if (isPMOContext && enhancedResponse.tasks.length > 0) {
        console.log(`PMOProjectsTaskAgent: PMO tasks extracted: ${enhancedResponse.tasks.map(t => `${t.title} (${t.category})`).join(', ')}`);
      }

      // Trigger modal notification for successful task extraction
      this._triggerTaskExtractionModal(enhancedResponse, isPMOContext, isPMOContext ? 'Gemini' : 'Groq', projectName, agentType);

      return enhancedResponse;

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks:', error);
      throw new Error(`Failed to extract tasks: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create tasks in Firebase using the addTask function and update hierarchical PMO structure
   */
  private async _createTasks(
    extractedTasks: ExtractedTask[],
    projectId: string,
    agentOutput: any
  ): Promise<Array<{ taskId: string; title: string; projectId: string; description: string }>> {
    const createdTasks = [];
    const createdTaskIds: string[] = [];

    for (const task of extractedTasks) {
      try {
        console.log(`PMOProjectsTaskAgent: Creating task: ${task.title}`);

        // Calculate dates
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        // Preserve team names instead of converting to user emails for consistency
        // This ensures the final tasks match what's shown in the preview
        const teamAssignment = task.teamAssignment || 'ADMIN';
        const taskAssignedTo = [teamAssignment];

        console.log(`PMOProjectsTaskAgent: Task "${task.title}"`);
        console.log(`  - Original teamAssignment: "${task.teamAssignment}"`);
        console.log(`  - LLM assignedTo field: [${task.assignedTo?.join(', ') || 'none'}]`);
        console.log(`  - Final preserved assignment: [${taskAssignedTo.join(', ')}]`);

        // Ensure we always have at least one assignment
        if (!taskAssignedTo || taskAssignedTo.length === 0) {
          console.warn(`PMOProjectsTaskAgent: No assignment for task "${task.title}", defaulting to ADMIN`);
          taskAssignedTo.push('ADMIN');
        }

        // Generate enhanced notes using LLM if agentOutput is available
        console.log(`PMOProjectsTaskAgent: Processing notes for task "${task.title}"`);
        console.log(`PMOProjectsTaskAgent: Original task.notes: "${task.notes || 'EMPTY'}"`);
        console.log(`PMOProjectsTaskAgent: AgentOutput available: ${!!agentOutput}`);

        let enhancedNotes = `Generated from Strategic Director Agent output.
TEAM ASSIGNMENT: ${task.teamAssignment || 'ADMIN'}
${task.estimatedDuration ? `Estimated duration: ${task.estimatedDuration}` : ''}
Original team from Strategic Analysis: ${task.teamAssignment || 'Not specified'}`;

        if (agentOutput) {
          try {
            console.log(`PMOProjectsTaskAgent: Attempting to generate enhanced notes for task "${task.title}"`);
            // Extract userId from agentOutput metadata for Firebase queries
            const userId = agentOutput.metadata?.userId || agentOutput.pmoMetadata?.userId || '<EMAIL>';
            const generatedNotes = await this._generateEnhancedNotes(task, agentOutput, projectId, userId);
            console.log(`PMOProjectsTaskAgent: Enhanced notes generation result: ${generatedNotes ? `SUCCESS (${generatedNotes.length} chars)` : 'EMPTY'}`);

            if (generatedNotes && generatedNotes.trim().length > 0) {
              enhancedNotes = generatedNotes;
              console.log(`PMOProjectsTaskAgent: ✅ Successfully generated enhanced notes for task "${task.title}" (${generatedNotes.length} characters)`);
            } else {
              console.warn(`PMOProjectsTaskAgent: ⚠️ Enhanced notes generation returned empty result for task "${task.title}"`);
            }
          } catch (notesError) {
            console.error(`PMOProjectsTaskAgent: ❌ Failed to generate enhanced notes for task "${task.title}":`, notesError);
            // Continue with default notes
          }
        } else {
          console.warn(`PMOProjectsTaskAgent: ⚠️ No agentOutput available for enhanced notes generation`);
        }

        // Prepare task data according to Task interface
        const taskData: Omit<Task, 'id'> = {
          projectId,
          title: task.title,
          description: task.description,
          category: task.category,
          status: 'Not Started' as TaskStatus,
          startDate,
          dueDate,
          assignedTo: taskAssignedTo, // Preserve team names as shown in preview
          priority: 'High' as TaskPriority, // Always HIGH priority as specified
          dependencies: task.dependencies || [],
          notes: enhancedNotes,
          createdBy: 'pmo-projects-task-agent',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Create the task using hierarchical subcollection structure
        const userId = agentOutput.metadata?.userId || agentOutput.pmoMetadata?.userId || '<EMAIL>';
        const pmoId = agentOutput.pmoMetadata?.pmoId || agentOutput.metadata?.pmoId;

        let taskId: string;
        let createdInSubcollection = false;

        if (pmoId) {
          // Use hierarchical structure - create task in project subcollection
          console.log(`PMOProjectsTaskAgent: Creating task in hierarchical subcollection structure - PMO: ${pmoId}, Project: ${projectId}`);

          // Generate task ID
          taskId = adminDb.collection('temp').doc().id; // Generate a unique ID

          // Add task to project subcollection
          const hierarchicalResult = await addTaskToProject(userId, pmoId, projectId, taskId, taskData);

          if (hierarchicalResult.success) {
            createdInSubcollection = true;
            console.log(`PMOProjectsTaskAgent: ✅ Successfully created task ${taskId} in subcollection structure`);
          } else {
            console.warn(`PMOProjectsTaskAgent: Failed to create task in hierarchical structure: ${hierarchicalResult.error}`);
            // Fallback to global tasks collection
            taskId = await addTask(taskData);
            console.log(`PMOProjectsTaskAgent: ⚠️ Fallback: Created task ${taskId} in global collection`);
          }
        } else {
          // Fallback to global tasks collection
          console.log(`PMOProjectsTaskAgent: No PMO ID found, using global tasks collection`);
          taskId = await addTask(taskData);
        }

        createdTaskIds.push(taskId);

        createdTasks.push({
          taskId,
          title: task.title,
          projectId,
          description: task.description,
          createdInSubcollection
        });

        console.log(`PMOProjectsTaskAgent: Successfully created task ${task.title} with ID: ${taskId}`);
        console.log(`  - Storage location: ${createdInSubcollection ? 'Hierarchical subcollection' : 'Global collection'}`);
        console.log(`  - Final task data:`, {
          assignedTo: taskData.assignedTo,
          category: taskData.category,
          createdBy: taskData.createdBy,
          teamFromNotes: task.teamAssignment
        });

      } catch (error) {
        console.error(`PMOProjectsTaskAgent: Error creating task ${task.title}:`, error);
        // Continue with other tasks even if one fails
      }
    }

    // Ensure project document exists in hierarchical structure
    if (createdTaskIds.length > 0 && agentOutput) {
      try {
        const userId = agentOutput.metadata?.userId || agentOutput.pmoMetadata?.userId || '<EMAIL>';
        const pmoId = agentOutput.pmoMetadata?.pmoId || agentOutput.metadata?.pmoId;

        if (pmoId) {
          console.log(`PMOProjectsTaskAgent: Ensuring project document exists in hierarchical structure for PMO ${pmoId}, Project ${projectId}`);

          // Get project information
          let projectInfo = {
            id: projectId,
            name: `Project ${projectId}`,
            createdAt: new Date(),
            status: 'Active'
          };

          try {
            const projectDoc = await adminDb.collection('projects').doc(projectId).get();
            if (projectDoc.exists) {
              const projectData = projectDoc.data();
              projectInfo = {
                id: projectId,
                name: projectData?.name || `Project ${projectId}`,
                createdAt: projectData?.createdAt?.toDate() || new Date(),
                status: projectData?.status || 'Active'
              };
            }
          } catch (error) {
            console.warn(`PMOProjectsTaskAgent: Could not fetch project data for ${projectId}:`, error);
          }

          // Ensure project document exists in hierarchical structure
          const projectResult = await createProjectDocument(userId, pmoId, projectId, projectInfo);

          if (projectResult.success) {
            console.log(`PMOProjectsTaskAgent: ✅ Successfully ensured project document exists in hierarchical structure`);
          } else {
            console.warn(`PMOProjectsTaskAgent: ⚠️ Failed to create project document in hierarchical structure: ${projectResult.error}`);
          }
        } else {
          console.warn(`PMOProjectsTaskAgent: ⚠️ No PMO ID found in agent output metadata, skipping hierarchical project creation`);
        }
      } catch (error) {
        console.error(`PMOProjectsTaskAgent: ❌ Error ensuring project document in hierarchical structure:`, error);
        // Don't fail the entire operation if hierarchical update fails
      }
    }

    return createdTasks;
  }

  /**
   * Map team names from Strategic Analysis to actual user assignments
   * Enhanced with comprehensive team name matching and proper user assignment
   */
  private _mapTeamToUsers(teamName?: string): string[] {
    const adminUser = '<EMAIL>';

    if (!teamName) {
      console.log(`PMOProjectsTaskAgent: No team name provided, defaulting to admin`);
      return [adminUser];
    }

    // Normalize team name for better matching
    const normalizedTeamName = teamName.trim().toLowerCase();

    // Enhanced team mappings with multiple variations
    const teamMappings: Record<string, string[]> = {
      // Research Team variations
      'research team': [adminUser],
      'research': [adminUser],
      'research & analysis': [adminUser],
      'research and analysis': [adminUser],

      // Marketing Team variations
      'marketing team': [adminUser],
      'marketing': [adminUser],
      'content creation': [adminUser],
      'content team': [adminUser],

      // Software Design Team variations
      'software design team': [adminUser],
      'software team': [adminUser],
      'design team': [adminUser],
      'development team': [adminUser],
      'software design': [adminUser],
      'implementation': [adminUser],

      // Sales Team variations
      'sales team': [adminUser],
      'sales': [adminUser],

      // Business Analysis Team variations
      'business analysis team': [adminUser],
      'business analysis': [adminUser],
      'strategy development': [adminUser],
      'quality assurance': [adminUser],

      // Admin variations
      'admin': [adminUser],
      'administration': [adminUser]
    };

    // Try exact match first (case-insensitive)
    if (teamMappings[normalizedTeamName]) {
      console.log(`PMOProjectsTaskAgent: Exact team match found for "${teamName}" -> [${teamMappings[normalizedTeamName].join(', ')}]`);
      return teamMappings[normalizedTeamName];
    }

    // Try partial matches with enhanced logic
    for (const [key, users] of Object.entries(teamMappings)) {
      if (normalizedTeamName.includes(key) || key.includes(normalizedTeamName)) {
        console.log(`PMOProjectsTaskAgent: Partial team match found for "${teamName}" -> "${key}" -> [${users.join(', ')}]`);
        return users;
      }
    }

    // Try keyword-based matching for common terms
    if (normalizedTeamName.includes('research') || normalizedTeamName.includes('analysis')) {
      console.log(`PMOProjectsTaskAgent: Keyword match (research/analysis) for "${teamName}" -> admin`);
      return [adminUser];
    }

    if (normalizedTeamName.includes('marketing') || normalizedTeamName.includes('content')) {
      console.log(`PMOProjectsTaskAgent: Keyword match (marketing/content) for "${teamName}" -> admin`);
      return [adminUser];
    }

    if (normalizedTeamName.includes('software') || normalizedTeamName.includes('design') || normalizedTeamName.includes('development')) {
      console.log(`PMOProjectsTaskAgent: Keyword match (software/design/development) for "${teamName}" -> admin`);
      return [adminUser];
    }

    if (normalizedTeamName.includes('sales')) {
      console.log(`PMOProjectsTaskAgent: Keyword match (sales) for "${teamName}" -> admin`);
      return [adminUser];
    }

    // Default to admin if no match found
    console.log(`PMOProjectsTaskAgent: No team mapping found for "${teamName}", defaulting to admin`);
    return [adminUser];
  }

  /**
   * Calculate due date based on start date and estimated duration using Calendar tool
   */
  private async _calculateDueDate(startDate: Date, estimatedDuration?: string): Promise<Date> {
    if (!estimatedDuration) {
      // Default to 7 days if no duration specified
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }

    // Parse duration string (e.g., "3 days", "2 weeks", "1 month")
    const durationMatch = estimatedDuration.match(/(\d+)\s*(day|week|month)s?/i);
    if (durationMatch) {
      const amount = parseInt(durationMatch[1]);
      const unit = durationMatch[2].toLowerCase();

      let daysToAdd = 7; // Default fallback
      switch (unit) {
        case 'day':
          daysToAdd = amount;
          break;
        case 'week':
          daysToAdd = amount * 7;
          break;
        case 'month':
          daysToAdd = amount * 30; // Approximate month as 30 days
          break;
      }

      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    } else {
      // If we can't parse the duration, default to 7 days
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Trigger task extraction success modal notification
   */
  private _triggerTaskExtractionModal(
    extractedTasks: ExtractedTasksResponse,
    isPMOContext: boolean,
    modelUsed: 'Gemini' | 'Groq',
    projectName: string,
    agentType: string
  ): void {
    try {
      // Calculate processing time (approximate)
      const processingTime = Math.round(Math.random() * 3 + 2); // 2-5 seconds simulation

      // Prepare task extraction result for modal
      const result = {
        taskCount: extractedTasks.tasks.length,
        extractionMode: isPMOContext ? 'PMO' as const : 'Standard' as const,
        modelUsed,
        tasks: extractedTasks.tasks.map(task => ({
          title: task.title,
          category: task.category,
          assignedTo: task.teamAssignment || 'ADMIN',
          dueDate: task.dueDate
        })),
        confidence: extractedTasks.confidence || 0.85,
        processingTime,
        projectName,
        agentType,
        requestId: `task-extraction-${Date.now()}`
      };

      // Use global notification system to trigger modal
      if (typeof window !== 'undefined') {
        // Client-side: dispatch custom event
        const event = new CustomEvent('taskExtractionSuccess', {
          detail: result
        });
        window.dispatchEvent(event);
        console.log('PMOProjectsTaskAgent: Dispatched taskExtractionSuccess event', result);
      } else {
        // Server-side: log for debugging
        console.log('PMOProjectsTaskAgent: Task extraction success (server-side)', result);
      }
    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error triggering task extraction modal:', error);
    }
  }

  /**
   * Stream update helper
   */
  private _streamUpdate(stage: PMOTaskStreamUpdate['stage'], data?: any, message?: string) {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }

  /**
   * Enhance task category using intelligent mapping based on content and team assignments
   */
  private _enhanceTaskCategory(task: ExtractedTask): string {
    const title = task.title.toLowerCase();
    const description = task.description.toLowerCase();
    const teamAssignment = (task.teamAssignment || '').toLowerCase();
    const content = `${title} ${description}`;

    // Research & Analysis patterns
    if (
      teamAssignment.includes('research') ||
      content.includes('research') ||
      content.includes('analysis') ||
      content.includes('analyze') ||
      content.includes('study') ||
      content.includes('investigate') ||
      content.includes('survey') ||
      content.includes('metrics') ||
      content.includes('data') ||
      content.includes('insights') ||
      content.includes('competitive') ||
      content.includes('market')
    ) {
      return 'Research & Analysis';
    }

    // Content Creation patterns
    if (
      content.includes('create content') ||
      content.includes('write') ||
      content.includes('messaging') ||
      content.includes('documentation') ||
      content.includes('copy') ||
      content.includes('content calendar') ||
      content.includes('editorial') ||
      content.includes('blog') ||
      content.includes('social media') ||
      content.includes('marketing materials') ||
      content.includes('brand') ||
      content.includes('creative')
    ) {
      return 'Content Creation';
    }

    // Strategy Development patterns
    if (
      content.includes('strategy') ||
      content.includes('plan') ||
      content.includes('framework') ||
      content.includes('roadmap') ||
      content.includes('approach') ||
      content.includes('methodology') ||
      content.includes('design') && (content.includes('strategic') || content.includes('approach')) ||
      content.includes('develop') && (content.includes('strategy') || content.includes('plan')) ||
      content.includes('strategic')
    ) {
      return 'Strategy Development';
    }

    // Quality Assurance patterns
    if (
      content.includes('review') ||
      content.includes('test') ||
      content.includes('validate') ||
      content.includes('verify') ||
      content.includes('audit') ||
      content.includes('check') ||
      content.includes('assess') ||
      content.includes('evaluate') ||
      content.includes('quality') ||
      content.includes('approval')
    ) {
      return 'Quality Assurance';
    }

    // Implementation patterns (default for remaining tasks)
    if (
      content.includes('implement') ||
      content.includes('execute') ||
      content.includes('deploy') ||
      content.includes('launch') ||
      content.includes('build') ||
      content.includes('develop') ||
      content.includes('setup') ||
      content.includes('configure') ||
      content.includes('install') ||
      teamAssignment.includes('software') ||
      teamAssignment.includes('sales') ||
      teamAssignment.includes('marketing') && !content.includes('content')
    ) {
      return 'Implementation';
    }

    // Default fallback based on team assignment
    if (teamAssignment.includes('research')) return 'Research & Analysis';
    if (teamAssignment.includes('marketing')) return 'Content Creation';
    if (teamAssignment.includes('software') || teamAssignment.includes('design')) return 'Implementation';
    if (teamAssignment.includes('sales')) return 'Implementation';

    // Final fallback
    return 'Implementation';
  }

  /**
   * Determine team assignment from agent output metadata
   */
  private _determineTeamAssignment(agentOutput: any): string {
    // Check PMO metadata first
    const pmoTeam = agentOutput.pmoMetadata?.teamName || agentOutput.pmoMetadata?.teamId;
    if (pmoTeam) {
      console.log(`PMOProjectsTaskAgent: Team from pmoMetadata: ${pmoTeam}`);
      return pmoTeam;
    }

    // Check general metadata
    const metadataTeam = agentOutput.metadata?.teamName || agentOutput.metadata?.teamId;
    if (metadataTeam) {
      console.log(`PMOProjectsTaskAgent: Team from metadata: ${metadataTeam}`);
      return metadataTeam;
    }

    // Determine from agent type
    const agentType = agentOutput.agentType;
    if (agentType === 'ResearchAgentManager') {
      console.log(`PMOProjectsTaskAgent: Team inferred from agentType: Research`);
      return 'Research';
    } else if (agentType === 'strategic-director') {
      console.log(`PMOProjectsTaskAgent: Team inferred from agentType: Marketing`);
      return 'Marketing';
    }

    // Check category for team hints
    const category = agentOutput.category || '';
    if (category.toLowerCase().includes('research')) {
      console.log(`PMOProjectsTaskAgent: Team inferred from category: Research`);
      return 'Research';
    } else if (category.toLowerCase().includes('marketing')) {
      console.log(`PMOProjectsTaskAgent: Team inferred from category: Marketing`);
      return 'Marketing';
    }

    console.log(`PMOProjectsTaskAgent: Could not determine team assignment, defaulting to Unknown`);
    return 'Unknown';
  }

  /**
   * Get expected agent type based on team assignment
   */
  private _getExpectedAgentType(teamAssignment: string): string | null {
    const team = teamAssignment.toLowerCase();

    if (team === 'research') {
      return 'ResearchAgentManager';
    } else if (team === 'marketing') {
      return 'strategic-director';
    }

    // Return null for unknown teams to avoid validation errors
    return null;
  }

  /**
   * Analyze strategic content structure to identify key sections and content types
   */
  private _analyzeStrategicContent(content: string): {
    hasTeamAssignments: boolean;
    hasTaskList: boolean;
    hasImplementationPlan: boolean;
    hasStrategicAnalysis: boolean;
    contentSections: string[];
    estimatedTaskCount: number;
  } {
    const lowerContent = content.toLowerCase();

    // Check for key sections
    const hasTeamAssignments = lowerContent.includes('team assignment') ||
                              lowerContent.includes('assigned to') ||
                              lowerContent.includes('task 1') ||
                              lowerContent.includes('task 2');

    const hasTaskList = lowerContent.includes('task ') ||
                       lowerContent.includes('action item') ||
                       lowerContent.includes('deliverable');

    const hasImplementationPlan = lowerContent.includes('implementation') ||
                                 lowerContent.includes('execution') ||
                                 lowerContent.includes('timeline');

    const hasStrategicAnalysis = lowerContent.includes('strategic') ||
                                lowerContent.includes('analysis') ||
                                lowerContent.includes('recommendation');

    // Identify content sections
    const sections = [];
    if (content.includes('# ')) sections.push('Markdown Headers');
    if (content.includes('## ')) sections.push('Subsections');
    if (content.includes('**')) sections.push('Bold Text');
    if (content.includes('- ') || content.includes('* ')) sections.push('Bullet Lists');
    if (content.includes('1. ') || content.includes('2. ')) sections.push('Numbered Lists');

    // Estimate task count based on content patterns
    const taskPatterns = [
      /task \d+/gi,
      /\d+\.\s+[A-Z]/g,
      /•\s*[A-Z]/g,
      /-\s*[A-Z]/g
    ];

    let estimatedTaskCount = 0;
    taskPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) estimatedTaskCount = Math.max(estimatedTaskCount, matches.length);
    });

    return {
      hasTeamAssignments,
      hasTaskList,
      hasImplementationPlan,
      hasStrategicAnalysis,
      contentSections: sections,
      estimatedTaskCount: Math.max(estimatedTaskCount, 5) // Minimum 5 tasks
    };
  }

  /**
   * Parse and validate LLM response using Zod schema with robust error handling
   */
  private _parseAndValidateResponse(response: string, isPMOContext: boolean): ExtractedTasksResponse {
    console.log(`PMOProjectsTaskAgent: Starting response parsing for ${isPMOContext ? 'Gemini' : 'Groq'} response`);

    // Strategy 1: Try direct JSON parsing with cleanup
    const jsonExtractionStrategies = [
      // Strategy 1: Look for complete JSON object with proper braces
      () => {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          let jsonString = jsonMatch[0];

          // Clean up common issues in the JSON
          jsonString = jsonString
            .replace(/ESOME\}/g, '}') // Remove malformed "ESOME}"
            .replace(/,\s*\}/g, '}')  // Remove trailing commas before closing braces
            .replace(/,\s*\]/g, ']')  // Remove trailing commas before closing brackets
            .replace(/\}\s*,\s*\]/g, '}]') // Fix malformed object endings in arrays
            .replace(/\n/g, ' ')      // Replace newlines with spaces
            .replace(/\s+/g, ' ')     // Normalize whitespace
            .trim();

          return jsonString;
        }
        return null;
      },

      // Strategy 2: Look for JSON between code blocks
      () => {
        const codeBlockMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
        return codeBlockMatch ? codeBlockMatch[1] : null;
      },

      // Strategy 3: Extract from first { to last }
      () => {
        const startIndex = response.indexOf('{');
        const endIndex = response.lastIndexOf('}');
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          return response.substring(startIndex, endIndex + 1);
        }
        return null;
      }
    ];

    let parsedResponse: any = null;
    let lastError: Error | null = null;

    // Try each extraction strategy
    for (let i = 0; i < jsonExtractionStrategies.length; i++) {
      try {
        const jsonString = jsonExtractionStrategies[i]();
        if (jsonString) {
          console.log(`PMOProjectsTaskAgent: Trying extraction strategy ${i + 1}: ${jsonString.substring(0, 200)}...`);
          parsedResponse = JSON.parse(jsonString);
          console.log(`PMOProjectsTaskAgent: ✅ Successfully parsed JSON using strategy ${i + 1}`);
          break;
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`PMOProjectsTaskAgent: Strategy ${i + 1} failed:`, lastError.message);
      }
    }

    if (!parsedResponse) {
      console.error(`PMOProjectsTaskAgent: All JSON extraction strategies failed. Last error:`, lastError);
      console.error(`PMOProjectsTaskAgent: Raw response: ${response.substring(0, 1000)}...`);

      // Fallback: Create a minimal valid response
      console.log(`PMOProjectsTaskAgent: Creating fallback response due to JSON parsing failure`);
      parsedResponse = {
        tasks: [],
        analysis: `Failed to parse ${isPMOContext ? 'Gemini' : 'Groq'} response. Raw response: ${response.substring(0, 500)}...`,
        confidence: 0.1,
        projectContext: 'Error in response parsing'
      };
    }

    // Validate using Zod schema with detailed error handling
    try {
      const validatedResponse = ExtractedTasksResponseSchema.parse(parsedResponse);
      console.log(`PMOProjectsTaskAgent: ✅ Successfully validated response with ${validatedResponse.tasks.length} tasks`);
      return validatedResponse;
    } catch (validationError) {
      console.error(`PMOProjectsTaskAgent: Zod validation failed:`, validationError);

      // Try to salvage what we can from the parsed response
      const salvageAttempt = this._salvagePartialResponse(parsedResponse, isPMOContext);

      try {
        const salvaged = ExtractedTasksResponseSchema.parse(salvageAttempt);
        console.log(`PMOProjectsTaskAgent: ✅ Successfully salvaged partial response with ${salvaged.tasks.length} tasks`);
        return salvaged;
      } catch (salvageError) {
        console.error(`PMOProjectsTaskAgent: Salvage attempt also failed:`, salvageError);

        // Final fallback: Return empty but valid response
        const fallbackResponse = {
          tasks: [],
          analysis: `Response validation failed. Original error: ${validationError instanceof Error ? validationError.message : String(validationError)}`,
          confidence: 0.0,
          projectContext: 'Validation error fallback'
        };

        return ExtractedTasksResponseSchema.parse(fallbackResponse);
      }
    }
  }

  /**
   * Attempt to salvage a partially valid response by fixing common issues
   */
  private _salvagePartialResponse(parsedResponse: any, isPMOContext: boolean): any {
    console.log(`PMOProjectsTaskAgent: Attempting to salvage partial response`);

    const salvaged: any = {
      tasks: [],
      analysis: parsedResponse.analysis || `Salvaged from ${isPMOContext ? 'Gemini' : 'Groq'} response`,
      confidence: typeof parsedResponse.confidence === 'number' ? parsedResponse.confidence : 0.5,
      projectContext: parsedResponse.projectContext || 'Salvaged response'
    };

    // Try to salvage tasks array
    if (Array.isArray(parsedResponse.tasks)) {
      salvaged.tasks = parsedResponse.tasks
        .filter((task: any) => task && typeof task === 'object')
        .map((task: any) => ({
          title: String(task.title || 'Untitled Task'),
          description: String(task.description || 'No description available'),
          category: String(task.category || 'General'),
          priority: ['Low', 'Medium', 'High'].includes(task.priority) ? task.priority : 'High',
          estimatedDuration: task.estimatedDuration ? String(task.estimatedDuration) : undefined,
          dependencies: Array.isArray(task.dependencies) ? task.dependencies.map(String) : [],
          notes: task.notes ? String(task.notes) : undefined,
          dueDate: task.dueDate ? String(task.dueDate) : undefined,
          startDate: task.startDate ? String(task.startDate) : undefined,
          assignedTo: Array.isArray(task.assignedTo) ? task.assignedTo.map(String) : [],
          teamAssignment: task.teamAssignment ? String(task.teamAssignment) : undefined
        }));
    }

    console.log(`PMOProjectsTaskAgent: Salvaged ${salvaged.tasks.length} tasks from partial response`);
    return salvaged;
  }

  /**
   * Generate enhanced notes for a task using LLM analysis of the PMO strategic output
   * Now includes proper TaskId mapping from Agent_Output to actual Firebase Project IDs
   * @param task - Task object
   * @param agentOutput - Agent output containing strategic analysis
   * @param pmoProjectId - PMO project ID (can be PMO ID or project ID)
   * @param userId - User ID for Firebase queries
   * @param url - Optional URL to extract specific project ID from
   */
  async _generateEnhancedNotes(
    task: any,
    agentOutput: any,
    pmoProjectId?: string,
    userId?: string,
    url?: string
  ): Promise<string> {
    try {
      console.log(`PMOProjectsTaskAgent: _generateEnhancedNotes called for task "${task.title}"`);

      // Extract the full strategic analysis content
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';

      console.log(`PMOProjectsTaskAgent: Content extraction - outputContent: ${outputContent.length} chars, thinkingContent: ${thinkingContent.length} chars, agentType: ${agentType}`);

      if (!outputContent || outputContent.trim().length === 0) {
        console.warn(`PMOProjectsTaskAgent: ❌ No strategic analysis content available for enhanced notes generation`);
        console.log(`PMOProjectsTaskAgent: AgentOutput structure:`, JSON.stringify(agentOutput, null, 2).substring(0, 500));
        return '';
      }

      // Extract PMO metadata for proper TaskId mapping
      const pmoMetadata = agentOutput.pmoMetadata || {};
      const pmoId = pmoMetadata.pmoId || agentOutput.metadata?.pmoId;
      const category = agentOutput.category || agentOutput.metadata?.category;

      // Extract userId from agentOutput metadata if not provided
      const effectiveUserId = userId || agentOutput.metadata?.userId || agentOutput.pmoMetadata?.userId || '<EMAIL>';

      console.log(`PMOProjectsTaskAgent: ✅ Generating enhanced notes for task "${task.title}" using ${outputContent.length} chars of strategic analysis`);
      console.log(`PMOProjectsTaskAgent: PMO Context - pmoId: ${pmoId}, category: ${category}, projectId: ${pmoProjectId}, userId: ${effectiveUserId}`);
      console.log(`PMOProjectsTaskAgent: Strategic content preview: ${outputContent.substring(0, 200)}...`);

      // Extract specific project ID from URL if provided
      const specificProjectId = this._extractProjectIdFromUrl(url);
      console.log(`PMOProjectsTaskAgent: Extracted project ID from URL: ${specificProjectId || 'None'}`);

      // Pre-process content to map Agent_Output TaskIds to actual Firebase Project IDs using task title for precise matching
      const processedContent = await this._mapTaskIdsToProjectIds(outputContent, pmoId, effectiveUserId, task.title, specificProjectId || undefined);

      // Create a comprehensive prompt for Gemini 2.5 to extract task-specific details
      const prompt = `
You are an expert Task Documentation Specialist analyzing comprehensive PMO strategic analysis to create detailed, actionable task notes.

IMPORTANT: When referencing task IDs in the content, use actual Firebase Project ID format (${pmoProjectId || 'Firebase Project ID'}) instead of any synthetic Agent Output task IDs.

STRATEGIC ANALYSIS CONTENT:
${processedContent}

${thinkingContent ? `STRATEGIC THINKING PROCESS:
${thinkingContent}` : ''}

SPECIFIC TASK TO DOCUMENT:
- Title: ${task.title}
- Description: ${task.description}
- Category: ${task.category}
- Team Assignment: ${task.teamAssignment || 'Not specified'}
- Estimated Duration: ${task.estimatedDuration || 'Not specified'}
- Dependencies: ${task.dependencies?.join(', ') || 'None specified'}
- PMO Project ID: ${pmoProjectId || pmoId || 'Not specified'}

TASK: Create comprehensive, detailed notes for this specific task that will serve as a complete reference for team members. The notes should be self-contained and actionable.

REQUIREMENTS:
1. Extract ALL relevant details from the strategic analysis that relate to this specific task
2. Include implementation guidance, requirements, and context
3. Identify any dependencies, prerequisites, or related considerations
4. Provide actionable steps and success criteria where available
5. Include relevant background context that helps understand WHY this task is important
6. Extract any specific requirements, constraints, or guidelines mentioned in the analysis
7. Include team coordination details and stakeholder information if mentioned
8. Use PMO Project ID format for any task references (${pmoProjectId || pmoId || 'Firebase Project ID'})

FORMAT: Provide a well-structured, comprehensive notes section that includes:
- Task Context & Background
- Detailed Requirements & Specifications
- Implementation Guidance
- Dependencies & Prerequisites
- Success Criteria & Deliverables
- Team Coordination Notes
- Additional Considerations

Make the notes comprehensive enough that a team member could understand and execute the task without needing to reference the original strategic analysis document.

ENHANCED TASK NOTES:`;

      // Use Gemini 2.5 for intelligent content extraction
      console.log(`PMOProjectsTaskAgent: Calling Gemini 2.5 Pro for enhanced notes generation...`);
      const { processWithGoogleAI } = await import('../../lib/tools/google-ai');
      const response = await processWithGoogleAI({
        prompt,
        model: 'gemini-2.5-pro'
      });

      console.log(`PMOProjectsTaskAgent: Gemini response received - Length: ${response?.length || 0}, Starts with FALLBACK: ${response?.startsWith('FALLBACK_REQUIRED:')}`);
      console.log(`PMOProjectsTaskAgent: Gemini response preview: ${response?.substring(0, 200) || 'EMPTY'}...`);

      if (response && response.trim().length > 0 && !response.startsWith('FALLBACK_REQUIRED:')) {
        // Get actual Firebase Project ID and Task ID for metadata from subcollections ONLY
        let actualProjectId = 'Not in subcollection structure';
        let actualTaskId = 'Not in subcollection structure';
        let dataSource = 'Subcollection structure not found';

        try {
          const firebaseIds = await this._getActualProjectIdFromPMO(pmoId, effectiveUserId, task.title);
          if (firebaseIds) {
            actualProjectId = firebaseIds.projectId;
            actualTaskId = firebaseIds.taskIds.length > 0 ? firebaseIds.taskIds[0] : 'No tasks in subcollection';
            dataSource = 'Firebase subcollection structure';
            console.log(`PMOProjectsTaskAgent: ✅ Retrieved actual Firebase IDs from subcollections - Project: ${actualProjectId}, Task: ${actualTaskId}`);
          } else {
            console.warn(`PMOProjectsTaskAgent: ⚠️ No subcollection structure found for PMO ${pmoId}`);
          }
        } catch (error) {
          console.warn(`PMOProjectsTaskAgent: ⚠️ Failed to retrieve actual Firebase IDs from subcollections:`, error);
        }

        // Add metadata footer to the enhanced notes with actual Firebase Project ID and Task ID
        const enhancedNotes = `${response.trim()}

---
TASK METADATA:
• Generated from: ${agentType} strategic analysis
• Team Assignment: ${task.teamAssignment || 'ADMIN'}
• Estimated Duration: ${task.estimatedDuration || 'Not specified'}
• Firebase Project ID: ${actualProjectId}
• Task ID: ${actualTaskId}
• PMO Record ID: ${pmoId || 'Not specified'}
• Created: ${new Date().toLocaleDateString()}
• Source: PMO Strategic Analysis (${Math.round(outputContent.length / 1000)}k chars)
• Data Flow: ${dataSource} → Firebase Document IDs`;

        console.log(`PMOProjectsTaskAgent: ✅ Successfully generated enhanced notes (${enhancedNotes.length} chars total)`);
        return enhancedNotes;
      } else {
        console.warn(`PMOProjectsTaskAgent: ❌ Gemini failed to generate enhanced notes, response: ${response?.substring(0, 100) || 'EMPTY'}`);
        return '';
      }

    } catch (error) {
      console.error(`PMOProjectsTaskAgent: Error generating enhanced notes:`, error);
      return '';
    }
  }

  /**
   * Map Agent_Output TaskIds to actual Firebase Project IDs and Task IDs
   * This ensures task notes display real Firebase document IDs from PMO collection
   * @param content - Content to process
   * @param pmoId - PMO document ID
   * @param userId - User ID for Firebase queries
   * @param taskTitle - Task title for matching
   * @param specificProjectId - Specific project ID from URL
   */
  private async _mapTaskIdsToProjectIds(content: string, pmoId?: string, userId?: string, taskTitle?: string, specificProjectId?: string): Promise<string> {
    if (!content) return content;

    try {
      console.log(`PMOProjectsTaskAgent: Starting Firebase-based TaskId mapping for PMO ID: ${pmoId}`);

      // Extract task title from content if not provided
      const extractedTaskTitle = taskTitle || this._extractTaskTitleFromContent(content);
      console.log(`PMOProjectsTaskAgent: 🎯 Using task title for precise matching: "${extractedTaskTitle}"`);

      // Get actual Project ID from PMO collection with task title for precise matching
      // If specificProjectId is provided, use it to target the exact project
      const actualProjectData = await this._getActualProjectIdFromPMO(pmoId, userId, extractedTaskTitle, specificProjectId);

      if (!actualProjectData) {
        console.warn(`PMOProjectsTaskAgent: Could not find actual Project ID for PMO ${pmoId}, skipping mapping`);
        return content;
      }

      const { projectId, taskIds } = actualProjectData;
      console.log(`PMOProjectsTaskAgent: Found actual Project ID: ${projectId}, Task IDs: [${taskIds.slice(0, 3).join(', ')}${taskIds.length > 3 ? '...' : ''}]`);

      // Enhanced patterns for TaskIds in Agent_Output content that need to be mapped
      // Priority order: Most specific patterns first to ensure proper replacement
      const taskIdPatterns = [
        // Synthetic PMO format IDs (highest priority - these MUST be replaced with actual Firebase IDs)
        /PMO Project ID:\s*(PMO-[a-f0-9]+)/gi, // "PMO Project ID: PMO-c76670a7" → actual Firebase Project ID
        /Project ID:\s*(PMO-[a-f0-9]+)/gi,     // "Project ID: PMO-c76670a7" → actual Firebase Project ID
        /\b(PMO-[a-f0-9]+)\b/gi,               // "PMO-c76670a7" → actual Firebase Project ID (standalone)

        // Task-specific patterns (replace with actual Firebase Task IDs)
        /Task ID:\s*([A-Z0-9_\-]+)/gi,         // "Task ID: RPT_001" → actual Firebase Task ID
        /TaskId:\s*([A-Z0-9_\-]+)/gi,          // "TaskId: RPT_001" → actual Firebase Task ID
        /Task\s+([A-Z0-9_\-]+)/gi,             // "Task RPT_001" → actual Firebase Task ID

        // Generic ID patterns (context-dependent replacement)
        /ID:\s*([A-Z0-9_\-]+)/gi,              // "ID: RPT_001" → context-dependent

        // Standalone task identifier patterns
        /\b([A-Z]{2,4}_\d{3})\b/g,             // "RPT_001", "MKT_001", "QA_001" → Firebase Task IDs
        /\b(Task-[A-Z0-9\-]+)\b/gi,            // "Task-001", "Task-ABC" → Firebase Task IDs
        /\b([A-Z]+\d{2,3})\b/g,                // "RPT001", "MKT001", "QA001" → Firebase Task IDs
        /\b(QA[_\-]?\d{3})\b/gi,               // "QA_001", "QA-001", "QA001" → Firebase Task IDs
        /\b(COMPLIANCE[_\-]?\d{3})\b/gi        // "COMPLIANCE_001", "COMPLIANCE-001" → Firebase Task IDs
      ];

      let processedContent = content;
      let replacementCount = 0;

      // Apply each pattern to map synthetic IDs to actual Firebase IDs
      taskIdPatterns.forEach((pattern, index) => {
        const matches = processedContent.match(pattern);
        if (matches) {
          console.log(`PMOProjectsTaskAgent: 🔍 Pattern ${index + 1} found ${matches.length} matches: ${matches.slice(0, 3).join(', ')}${matches.length > 3 ? '...' : ''}`);

          processedContent = processedContent.replace(pattern, (match, capturedId) => {
            // Skip if it's already an actual Firebase document ID
            if (match.includes(projectId) || taskIds.includes(capturedId)) {
              console.log(`PMOProjectsTaskAgent: ⏭️ Skipping already mapped ID: ${match}`);
              return match;
            }

            replacementCount++;
            const lowerMatch = match.toLowerCase();

            // Priority replacement logic for synthetic PMO format IDs
            if (capturedId && capturedId.startsWith('PMO-')) {
              console.log(`PMOProjectsTaskAgent: 🔄 Replacing synthetic PMO ID "${capturedId}" with actual Firebase Project ID "${projectId}"`);
              return match.replace(capturedId, projectId);
            }

            // Task-specific replacements with actual Firebase Task IDs
            else if (lowerMatch.includes('task id:') || lowerMatch.includes('taskid:')) {
              const replacementId = taskIds.length > 0 ? taskIds[0] : projectId;
              console.log(`PMOProjectsTaskAgent: 🔄 Replacing Task ID "${capturedId}" with Firebase Task ID "${replacementId}"`);
              return match.replace(capturedId, replacementId);
            }

            // Project ID context - use actual Firebase Project ID
            else if (lowerMatch.includes('project id:') || lowerMatch.includes('id:')) {
              console.log(`PMOProjectsTaskAgent: 🔄 Replacing Project ID "${capturedId}" with Firebase Project ID "${projectId}"`);
              return match.replace(capturedId, projectId);
            }

            // Task context - use actual Firebase Task IDs
            else if (lowerMatch.includes('task ')) {
              const replacementId = taskIds.length > 0 ? taskIds[0] : projectId;
              console.log(`PMOProjectsTaskAgent: 🔄 Replacing Task reference "${capturedId}" with Firebase Task ID "${replacementId}"`);
              return match.replace(capturedId, replacementId);
            }

            // Standalone task identifiers - distribute across available Firebase Task IDs
            else {
              if (taskIds.length > 0) {
                const taskIndex = (replacementCount - 1) % taskIds.length;
                const replacementId = taskIds[taskIndex];
                console.log(`PMOProjectsTaskAgent: 🔄 Replacing standalone ID "${capturedId}" with Firebase Task ID "${replacementId}" (index ${taskIndex})`);
                return replacementId;
              } else {
                console.log(`PMOProjectsTaskAgent: 🔄 No tasks available, replacing "${capturedId}" with Firebase Project ID "${projectId}"`);
                return projectId;
              }
            }
          });
        }
      });

      if (replacementCount > 0) {
        console.log(`PMOProjectsTaskAgent: ✅ Successfully replaced ${replacementCount} synthetic IDs with actual Firebase IDs`);
        console.log(`PMOProjectsTaskAgent: 📋 Using Firebase Project ID: ${projectId}`);
        console.log(`PMOProjectsTaskAgent: 📋 Using Firebase Task IDs: [${taskIds.slice(0, 3).join(', ')}${taskIds.length > 3 ? ` + ${taskIds.length - 3} more` : ''}]`);
      } else {
        console.log(`PMOProjectsTaskAgent: ℹ️ No synthetic ID patterns found to replace in content`);
      }

      return processedContent;

    } catch (error) {
      console.error(`PMOProjectsTaskAgent: Error mapping TaskIds to ProjectIds:`, error);
      return content; // Return original content if mapping fails
    }
  }

  /**
   * Extract task title from content for precise Firebase matching
   * Looks for common task title patterns in the content
   */
  private _extractTaskTitleFromContent(content: string): string {
    if (!content) return 'Execute Multi-Stage Quality Assurance Protocol'; // Default fallback

    // Common patterns for task titles in content
    const titlePatterns = [
      /Task Title:\s*([^\n\r]+)/i,
      /Title:\s*([^\n\r]+)/i,
      /Task:\s*([^\n\r]+)/i,
      /Execute\s+([^\n\r]+)/i,
      /Implement\s+([^\n\r]+)/i,
      /Perform\s+([^\n\r]+)/i,
      /Conduct\s+([^\n\r]+)/i,
      /Quality\s+Assurance\s+([^\n\r]+)/i,
      /Compliance\s+([^\n\r]+)/i
    ];

    for (const pattern of titlePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const extractedTitle = match[1].trim();
        console.log(`PMOProjectsTaskAgent: 📋 Extracted task title from content: "${extractedTitle}"`);
        return extractedTitle;
      }
    }

    // If no specific pattern found, look for quality assurance or compliance related content
    if (content.toLowerCase().includes('quality assurance')) {
      return 'Execute Multi-Stage Quality Assurance Protocol';
    } else if (content.toLowerCase().includes('compliance')) {
      return 'Compliance Review and Validation';
    } else if (content.toLowerCase().includes('strategic')) {
      return 'Strategic Analysis and Planning';
    }

    console.log(`PMOProjectsTaskAgent: ⚠️ No task title pattern found in content, using default`);
    return 'Execute Multi-Stage Quality Assurance Protocol'; // Default fallback
  }

  /**
   * Extract project ID from URL path
   * @param url - URL to extract project ID from
   * @returns Project ID or null
   */
  private _extractProjectIdFromUrl(url?: string): string | null {
    if (!url) return null;

    // Look for project ID in URL patterns like:
    // /services/admin/planner/UojSomTZAvbYINlFiFBu
    // /admin/planner/UojSomTZAvbYINlFiFBu
    const urlPatterns = [
      /\/planner\/([a-zA-Z0-9]+)(?:\/|$)/,
      /\/admin\/planner\/([a-zA-Z0-9]+)(?:\/|$)/,
      /\/services\/admin\/planner\/([a-zA-Z0-9]+)(?:\/|$)/
    ];

    for (const pattern of urlPatterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        console.log(`PMOProjectsTaskAgent: Extracted project ID from URL: ${match[1]}`);
        return match[1];
      }
    }

    console.warn(`PMOProjectsTaskAgent: Could not extract project ID from URL: ${url}`);
    return null;
  }

  /**
   * Get actual Project ID and Task IDs from Firebase subcollections ONLY
   * ONLY uses hierarchical subcollection structure - no fallback to legacy arrays
   * Returns actual Firebase document IDs for proper data consistency
   * @param pmoId - PMO document ID
   * @param userId - User email
   * @param taskTitle - Optional task title for specific task matching
   * @param specificProjectId - Optional specific project ID to target (from URL)
   */
  private async _getActualProjectIdFromPMO(pmoId?: string, userId?: string, taskTitle?: string, specificProjectId?: string): Promise<{projectId: string, taskIds: string[]} | null> {
    if (!pmoId || !userId) {
      console.warn('PMOProjectsTaskAgent: Missing pmoId or userId for Firebase query');
      return null;
    }

    try {
      console.log(`PMOProjectsTaskAgent: 🔍 Querying PMO subcollections for actual Firebase Project and Task IDs - PMO: ${pmoId}, User: ${userId}, Task: ${taskTitle || 'N/A'}`);

      // ONLY use hierarchical subcollection structure
      const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);

      if (hierarchicalResult.success && hierarchicalResult.data && hierarchicalResult.data.length > 0) {
        console.log(`PMOProjectsTaskAgent: ✅ Using hierarchical subcollection structure for PMO ${pmoId}`);

        // If specific project ID is provided, use that project; otherwise use the most recent project
        let projectData: HierarchicalPMOData;

        if (specificProjectId) {
          // Find the specific project in the hierarchical data
          const targetProject = hierarchicalResult.data.find(p => p.projectId === specificProjectId);
          if (targetProject) {
            projectData = targetProject;
            console.log(`PMOProjectsTaskAgent: ✅ Found specific project ${specificProjectId} in hierarchical data`);
          } else {
            console.warn(`PMOProjectsTaskAgent: ⚠️ Specific project ${specificProjectId} not found, using most recent project`);
            projectData = hierarchicalResult.data[hierarchicalResult.data.length - 1];
          }
        } else {
          // Get the most recent project (last in array) - properly typed as HierarchicalPMOData
          projectData = hierarchicalResult.data[hierarchicalResult.data.length - 1];
        }

        const actualProjectId = projectData.projectId;
        let orderedTaskIds = [...projectData.taskIds];

        console.log(`PMOProjectsTaskAgent: ✅ Found actual Firebase Project ID: ${actualProjectId} from ${hierarchicalResult.data.length} projects (subcollection)`);
        console.log(`PMOProjectsTaskAgent: ✅ Found ${orderedTaskIds.length} actual Firebase Task IDs: ${orderedTaskIds.slice(0, 3).join(', ')}${orderedTaskIds.length > 3 ? '...' : ''} (subcollection)`);

        // If taskTitle is provided, try to find the specific task ID using getProjectTasks
        if (taskTitle && orderedTaskIds.length > 0) {
          try {
            console.log(`PMOProjectsTaskAgent: 🎯 Searching for specific task with title: "${taskTitle}" using getProjectTasks`);

            // Use getProjectTasks to get full task data from subcollection
            const projectTasksResult = await getProjectTasks(userId, pmoId, actualProjectId, true);

            if (projectTasksResult.success && projectTasksResult.tasks) {
              // Find the specific task by title
              const specificTask = projectTasksResult.tasks.find(task => task.title === taskTitle);

              if (specificTask) {
                const specificTaskId = specificTask.id;
                console.log(`PMOProjectsTaskAgent: 🎯 Found specific task ID for "${taskTitle}": ${specificTaskId} using getProjectTasks`);
                // Put the specific task ID first in the array
                orderedTaskIds = [specificTaskId, ...orderedTaskIds.filter((id: string) => id !== specificTaskId)];
              } else {
                console.log(`PMOProjectsTaskAgent: ⚠️ No specific task found for title "${taskTitle}" in subcollection, using all task IDs`);
              }
            } else {
              console.log(`PMOProjectsTaskAgent: ⚠️ Failed to get project tasks: ${projectTasksResult.error}, using all task IDs`);
            }
          } catch (error) {
            console.warn(`PMOProjectsTaskAgent: ⚠️ Error using getProjectTasks for specific task search, using all task IDs:`, error);
          }
        }

        return {
          projectId: actualProjectId,
          taskIds: orderedTaskIds
        };
      }

      // NO FALLBACK - only return data if subcollection structure exists
      console.warn(`PMOProjectsTaskAgent: ❌ No hierarchical subcollection structure found for PMO ${pmoId} - returning null`);
      return null;

    } catch (error) {
      console.error(`PMOProjectsTaskAgent: ❌ Error getting actual Project ID from PMO subcollections:`, error);
      return null;
    }
  }

  /**
   * Validate task metadata consistency using getProjectTasks
   * Ensures task metadata matches actual subcollection structure
   */
  async validateTaskMetadataConsistency(
    userId: string,
    pmoId: string,
    projectId: string,
    taskId: string,
    expectedTaskTitle?: string
  ): Promise<{
    success: boolean;
    isConsistent: boolean;
    actualTask?: Task;
    error?: string;
  }> {
    try {
      console.log(`PMOProjectsTaskAgent: Validating task metadata consistency using getProjectTasks`);

      // Use getProjectTasks to get all tasks with full data
      const projectTasksResult = await getProjectTasks(userId, pmoId, projectId, true);

      if (!projectTasksResult.success || !projectTasksResult.tasks) {
        return {
          success: false,
          isConsistent: false,
          error: `Failed to get project tasks: ${projectTasksResult.error}`
        };
      }

      // Find the specific task by ID
      const actualTask = projectTasksResult.tasks.find(task => task.id === taskId);

      if (!actualTask) {
        return {
          success: true,
          isConsistent: false,
          error: `Task ${taskId} not found in project ${projectId} subcollection`
        };
      }

      // Validate task title if provided
      if (expectedTaskTitle && actualTask.title !== expectedTaskTitle) {
        return {
          success: true,
          isConsistent: false,
          actualTask,
          error: `Task title mismatch: expected "${expectedTaskTitle}", found "${actualTask.title}"`
        };
      }

      console.log(`PMOProjectsTaskAgent: ✅ Task metadata is consistent with subcollection structure`);
      return {
        success: true,
        isConsistent: true,
        actualTask
      };

    } catch (error) {
      console.error(`PMOProjectsTaskAgent: ❌ Error validating task metadata consistency:`, error);
      return {
        success: false,
        isConsistent: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get all tasks for a specific project using hierarchical subcollection structure
   * Uses getProjectTasks function for proper subcollection access
   */
  async getProjectTasksFromSubcollection(
    userId: string,
    pmoId: string,
    projectId: string,
    includeTaskData: boolean = false
  ): Promise<{
    success: boolean;
    taskIds?: string[];
    tasks?: Task[];
    error?: string;
  }> {
    try {
      console.log(`PMOProjectsTaskAgent: Getting tasks for project ${projectId} using getProjectTasks`);

      const result = await getProjectTasks(userId, pmoId, projectId, includeTaskData);

      if (result.success) {
        console.log(`PMOProjectsTaskAgent: ✅ Successfully retrieved ${result.taskIds?.length || 0} tasks from subcollection`);
        return result;
      } else {
        console.warn(`PMOProjectsTaskAgent: ⚠️ Failed to get project tasks: ${result.error}`);
        return result;
      }
    } catch (error) {
      console.error(`PMOProjectsTaskAgent: ❌ Error getting project tasks from subcollection:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// Export a default instance
export const pmoProjectsTaskAgent = new PMOProjectsTaskAgent({
  includeExplanation: true,
  streamResponse: false
});