'use client';

import React, { useState } from 'react';
import { Task } from '../types';
import { X, Edit, Play, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import MarkdownRenderer from '../../../components/MarkdownRenderer';

interface TaskDetailsModalProps {
  task: Task;
  onClose: () => void;
  onEdit?: (task: Task) => void;
}

const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  task,
  onClose,
  onEdit
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processResult, setProcessResult] = useState<string | null>(null);

  // Check if this is a PMO-generated task
  const isPMOTask = task.createdBy === 'pmo-projects-task-agent' ||
                   (task.notes && task.notes.includes('Generated from Strategic Director Agent output'));

  // Extract team assignment from task notes
  const getTeamAssignment = (): string | null => {
    if (!task.notes) return null;
    const teamMatch = task.notes.match(/TEAM ASSIGNMENT: ([^\n]+)/);
    return teamMatch ? teamMatch[1].trim() : null;
  };

  // Extract PMO metadata from task notes for debugging
  const getPMOMetadata = (): { pmoId?: string; projectId?: string; actualProjectId?: string } => {
    if (!task.notes) return {};

    const pmoIdMatch = task.notes.match(/PMO Record ID: ([^\n]+)/);
    const projectIdMatch = task.notes.match(/PMO Project ID: ([^\n]+)/);

    // The actual Firebase Project ID is stored in task.projectId
    const actualProjectId = task.projectId;

    return {
      pmoId: pmoIdMatch ? pmoIdMatch[1].trim() : undefined,
      projectId: projectIdMatch ? projectIdMatch[1].trim() : undefined,
      actualProjectId: actualProjectId
    };
  };

  // Handle task processing
  const handleProcessTask = async () => {
    setIsProcessing(true);
    setProcessResult(null);

    try {
      const teamAssignment = getTeamAssignment();
      const assignedUsers = task.assignedTo || [];
      const pmoMetadata = getPMOMetadata();

      console.log(`Processing PMO task: ${task.title}`);
      console.log(`  - Task ID: ${task.id}`);
      console.log(`  - Actual Firebase Project ID: ${pmoMetadata.actualProjectId}`);
      console.log(`  - PMO Record ID: ${pmoMetadata.pmoId || 'Not found in notes'}`);
      console.log(`  - Legacy PMO Project ID: ${pmoMetadata.projectId || 'Not found in notes'}`);
      console.log(`  - Team Assignment: ${teamAssignment}`);
      console.log(`  - Assigned Users: [${assignedUsers.join(', ')}]`);
      console.log(`  - Category: ${task.category}`);
      console.log(`  - Task Notes Preview: ${task.notes?.substring(0, 200) || 'No notes'}...`);

      // Special logging for the specific PMO ID mentioned in the issue
      if (pmoMetadata.pmoId === 'c76670a7-bc7b-44ea-9905-189a4bcf36c8') {
        console.log(`🔍 DEBUGGING SPECIFIC PMO ID: c76670a7-bc7b-44ea-9905-189a4bcf36c8`);
        console.log(`  - Actual Firebase Project ID: ${pmoMetadata.actualProjectId}`);
        console.log(`  - Legacy PMO Project ID: ${pmoMetadata.projectId}`);
        console.log(`  - Expected Project ID format: ywgeXa9Ppz33JwxTjw0M (example)`);
        console.log(`  - TaskId mapping status: ${pmoMetadata.actualProjectId ? 'USING ACTUAL FIREBASE ID' : 'NO PROJECT ID'}`);
        console.log(`  - Data flow: PMO Collection → generatedProjects[latest] → Firebase Project ID`);
      }

      const response = await fetch('/api/process-pmo-task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: task.id,
          taskTitle: task.title,
          taskDescription: task.description,
          teamAssignment: teamAssignment,
          assignedUsers: assignedUsers,
          projectId: task.projectId,
          category: task.category
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to process task: ${response.statusText}`);
      }

      const result = await response.json();
      setProcessResult(result.message || 'Task processed successfully');
      console.log('Task processing result:', result);

    } catch (error) {
      console.error('Error processing task:', error);
      setProcessResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Function to get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'Critical': return 'bg-red-900/50 text-red-300';
      case 'High': return 'bg-orange-900/50 text-orange-300';
      case 'Medium': return 'bg-yellow-900/50 text-yellow-300';
      case 'Low': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  // Function to get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Not Started': return 'bg-gray-700 text-gray-300';
      case 'In Progress': return 'bg-blue-900/50 text-blue-300';
      case 'Blocked': return 'bg-red-900/50 text-red-300';
      case 'Complete': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  // Format date for display
  const formatDate = (dateString: string | Date): string => {
    if (!dateString) return 'Not set';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Handle edit button click
  const handleEdit = () => {
    if (onEdit) {
      onEdit(task);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-white">{task.title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-gray-400 text-sm mb-1">Status</p>
            <div className={`px-3 py-1 rounded-full text-sm inline-block ${getStatusColor(task.status)}`}>
              {task.status}
            </div>
          </div>
          <div>
            <p className="text-gray-400 text-sm mb-1">Priority</p>
            <div className={`px-3 py-1 rounded-full text-sm inline-block ${getPriorityColor(task.priority)}`}>
              {task.priority}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-gray-400 text-sm mb-1">Start Date</p>
            <p className="text-white">{formatDate(task.startDate)}</p>
          </div>
          <div>
            <p className="text-gray-400 text-sm mb-1">Due Date</p>
            <p className="text-white">{formatDate(task.dueDate)}</p>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Category</p>
          <p className="text-white">{task.category || 'Not categorized'}</p>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Description</p>
          <div className="bg-gray-700/50 p-3 rounded text-white">
            {task.description || 'No description provided.'}
          </div>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Assigned To</p>
          <div className="flex flex-wrap gap-2">
            {task.assignedTo && task.assignedTo.length > 0 ? (
              task.assignedTo.map((assignee, index) => (
                <span key={index} className="bg-purple-500/30 text-purple-200 px-2 py-1 rounded text-xs">
                  {assignee}
                </span>
              ))
            ) : (
              <span className="text-gray-400">Not assigned</span>
            )}
          </div>
        </div>

        {task.notes && (
          <div className="mb-4">
            <p className="text-gray-400 text-sm mb-1">Notes</p>
            {/* Task notes now display TaskIds that align with PMO collection ProjectId field */}
            <div className="bg-gray-700/50 rounded max-h-[400px] overflow-y-auto">
              <MarkdownRenderer content={task.notes} />
            </div>
          </div>
        )}

        {/* Process result display */}
        {processResult && (
          <div className="mb-4">
            <p className="text-gray-400 text-sm mb-1">Processing Result</p>
            <div className="bg-green-700/50 p-3 rounded text-green-200 text-sm">
              {processResult}
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          {/* Show Process button only for PMO tasks */}
          {isPMOTask && (
            <button
              onClick={handleProcessTask}
              disabled={isProcessing}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Process Task
                </>
              )}
            </button>
          )}

          <button
            onClick={handleEdit}
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors flex items-center"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Task
          </button>
        </div>
      </div>
    </div>
  );
};

export default TaskDetailsModal;
