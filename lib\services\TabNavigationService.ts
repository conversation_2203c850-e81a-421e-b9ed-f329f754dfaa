/**
 * Tab Navigation Service for PMO Workflow
 *
 * This service manages automatic tab navigation functionality for the PMO workflow,
 * specifically handling automatic switching to the "PMO Output" tab when research
 * or marketing processing is completed successfully.
 */

export interface TabNavigationEvent {
  type: 'research-completed' | 'marketing-completed' | 'task-completed';
  data: {
    requestId?: string;
    pmoId?: string;
    teamName?: string;
    success: boolean;
    error?: string;
    timestamp: string;
    agentType?: string;
  };
}

export interface TabNavigationListener {
  (event: TabNavigationEvent): void;
}

class TabNavigationService {
  private listeners: Map<string, TabNavigationListener[]> = new Map();
  private eventHistory: TabNavigationEvent[] = [];
  private maxHistorySize = 50;

  /**
   * Subscribe to tab navigation events
   */
  subscribe(eventType: string, listener: TabNavigationListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }

    this.listeners.get(eventType)!.push(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(eventType);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  /**
   * Emit a tab navigation event
   */
  emit(event: TabNavigationEvent): void {
    console.log(`[TabNavigationService] Emitting event:`, event);

    // Add to history
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // Notify listeners
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`[TabNavigationService] Error in listener for ${event.type}:`, error);
        }
      });
    }

    // Also notify 'all' listeners
    const allListeners = this.listeners.get('all');
    if (allListeners) {
      allListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`[TabNavigationService] Error in 'all' listener:`, error);
        }
      });
    }
  }

  /**
   * Emit research completion event
   */
  emitResearchCompleted(data: {
    requestId?: string;
    pmoId?: string;
    success: boolean;
    error?: string;
  }): void {
    this.emit({
      type: 'research-completed',
      data: {
        ...data,
        teamName: 'Research',
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Emit marketing completion event
   */
  emitMarketingCompleted(data: {
    requestId?: string;
    pmoId?: string;
    success: boolean;
    error?: string;
  }): void {
    this.emit({
      type: 'marketing-completed',
      data: {
        ...data,
        teamName: 'Marketing',
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Emit generic task completion event
   */
  emitTaskCompleted(data: {
    requestId?: string;
    pmoId?: string;
    teamName?: string;
    success: boolean;
    error?: string;
  }): void {
    this.emit({
      type: 'task-completed',
      data: {
        ...data,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Get recent events
   */
  getRecentEvents(limit: number = 10): TabNavigationEvent[] {
    return this.eventHistory.slice(-limit);
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Get listener count for debugging
   */
  getListenerCount(eventType?: string): number {
    if (eventType) {
      return this.listeners.get(eventType)?.length || 0;
    }

    let total = 0;
    this.listeners.forEach(listeners => {
      total += listeners.length;
    });
    return total;
  }

  /**
   * Check if there are any listeners for an event type
   */
  hasListeners(eventType: string): boolean {
    const listeners = this.listeners.get(eventType);
    return listeners ? listeners.length > 0 : false;
  }
}

// Create singleton instance
export const tabNavigationService = new TabNavigationService();

// Browser-specific event handling for cross-tab communication
if (typeof window !== 'undefined') {
  // Listen for custom events from other parts of the application
  window.addEventListener('pmo-research-completed', (event: any) => {
    tabNavigationService.emitResearchCompleted(event.detail);
  });

  window.addEventListener('pmo-marketing-completed', (event: any) => {
    tabNavigationService.emitMarketingCompleted(event.detail);
  });

  window.addEventListener('pmo-task-completed', (event: any) => {
    tabNavigationService.emitTaskCompleted(event.detail);
  });
}

/**
 * Dispatch a tab navigation event to trigger automatic tab switching
 */
export function dispatchTabNavigationEvent(
  eventType: 'research-completed' | 'marketing-completed' | 'task-completed',
  data: {
    requestId?: string;
    pmoId?: string;
    teamName?: string;
    success: boolean;
    error?: string;
    agentType?: string;
  }
): void {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent(`pmo-${eventType}`, {
      detail: {
        ...data,
        timestamp: new Date().toISOString()
      },
      bubbles: true
    });

    console.log(`[TabNavigationService] Dispatching ${eventType} event:`, event.detail);
    window.dispatchEvent(event);
  }
}

/**
 * React hook for using tab navigation service
 */
export function useTabNavigation() {
  return {
    subscribe: tabNavigationService.subscribe.bind(tabNavigationService),
    emitResearchCompleted: tabNavigationService.emitResearchCompleted.bind(tabNavigationService),
    emitMarketingCompleted: tabNavigationService.emitMarketingCompleted.bind(tabNavigationService),
    emitTaskCompleted: tabNavigationService.emitTaskCompleted.bind(tabNavigationService),
    getRecentEvents: tabNavigationService.getRecentEvents.bind(tabNavigationService),
    hasListeners: tabNavigationService.hasListeners.bind(tabNavigationService)
  };
}

export default tabNavigationService;
