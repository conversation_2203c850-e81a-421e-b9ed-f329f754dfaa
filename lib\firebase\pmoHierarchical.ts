// lib/firebase/pmoHierarchical.ts
import { adminDb } from '../../components/firebase-admin';
import { PMOProjectTaskMapping } from '../agents/pmo/PMOInterfaces';
import { Task } from '../../admin/planner/types';

/**
 * Hierarchical PMO Firebase utilities for the new project-task subcollection structure
 * Structure: users/{userId}/PMO/{pmoId}/projects/{projectId}/tasks/{taskId}
 */

export interface HierarchicalPMOData {
  projectId: string;
  taskIds: string[];
  projectInfo?: {
    id: string;
    name: string;
    createdAt: Date;
    status: string;
  };
}

/**
 * Get the Firebase path for a project's tasks subcollection
 */
export function getProjectTasksCollectionPath(userId: string, pmoId: string, projectId: string): string {
  return `users/${userId}/PMO/${pmoId}/projects/${projectId}/tasks`;
}

/**
 * Get the Firebase path for a specific task in a project
 */
export function getProjectTaskPath(userId: string, pmoId: string, projectId: string, taskId: string): string {
  return `${getProjectTasksCollectionPath(userId, pmoId, projectId)}/${taskId}`;
}

/**
 * Get the Firebase path for a project document
 */
export function getProjectPath(userId: string, pmoId: string, projectId: string): string {
  return `users/${userId}/PMO/${pmoId}/projects/${projectId}`;
}

export interface PMOHierarchicalResult {
  success: boolean;
  data?: HierarchicalPMOData[];
  error?: string;
}

/**
 * Get hierarchical project-task data from PMO subcollections
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @returns Hierarchical project-task data
 */
export async function getHierarchicalPMOData(
  userId: string,
  pmoId: string
): Promise<PMOHierarchicalResult> {
  try {
    console.log(`PMOHierarchical: Getting hierarchical data for PMO ${pmoId}, User: ${userId}`);

    // Get all projects in this PMO
    const projectsCollection = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects');

    const projectsSnapshot = await projectsCollection.get();

    if (projectsSnapshot.empty) {
      // Fallback to legacy structure
      console.log(`PMOHierarchical: No projects subcollection found, checking legacy structure for PMO ${pmoId}`);
      return await getLegacyHierarchicalData(userId, pmoId);
    }

    const hierarchicalData: HierarchicalPMOData[] = [];

    for (const projectDoc of projectsSnapshot.docs) {
      const projectId = projectDoc.id;
      const projectData = projectDoc.data();

      // Get all tasks for this project
      const tasksCollection = projectDoc.ref.collection('tasks');
      const tasksSnapshot = await tasksCollection.get();

      const taskIds = tasksSnapshot.docs.map(taskDoc => taskDoc.id);

      hierarchicalData.push({
        projectId,
        taskIds,
        projectInfo: {
          id: projectId,
          name: projectData.name || `Project ${projectId}`,
          createdAt: projectData.createdAt?.toDate() || new Date(),
          status: projectData.status || 'Active'
        }
      });

      console.log(`PMOHierarchical: Found project ${projectId} with ${taskIds.length} tasks`);
    }

    console.log(`PMOHierarchical: Successfully retrieved ${hierarchicalData.length} projects from subcollections`);

    return {
      success: true,
      data: hierarchicalData
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting hierarchical data:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Fallback function to get data from legacy flat structure
 */
async function getLegacyHierarchicalData(
  userId: string,
  pmoId: string
): Promise<PMOHierarchicalResult> {
  try {
    const pmoDoc = await adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .get();

    if (!pmoDoc.exists) {
      return {
        success: false,
        error: `PMO document ${pmoId} not found`
      };
    }

    const pmoData = pmoDoc.data();
    const projectIds = pmoData?.projectIds || [];
    const taskIds = pmoData?.taskIds || [];

    if (projectIds.length === 0) {
      return {
        success: false,
        error: `No projects found in PMO ${pmoId}`
      };
    }

    console.log(`PMOHierarchical: Using legacy flat arrays for PMO ${pmoId} - ${projectIds.length} projects, ${taskIds.length} tasks`);

    // Convert legacy structure to hierarchical format
    const legacyData: HierarchicalPMOData[] = projectIds.map((projectId: string) => ({
      projectId,
      taskIds: taskIds, // In legacy structure, all tasks are associated with all projects
    }));

    return {
      success: true,
      data: legacyData
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting legacy hierarchical data:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Create a project document in the hierarchical structure
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID
 * @param projectInfo - Project information
 */
export async function createProjectDocument(
  userId: string,
  pmoId: string,
  projectId: string,
  projectInfo: {
    id: string;
    name: string;
    createdAt: Date;
    status: string;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`PMOHierarchical: Creating project document for PMO ${pmoId}, Project: ${projectId}`);

    const projectDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects')
      .doc(projectId);

    await projectDocRef.set({
      ...projectInfo,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Also update legacy arrays for backward compatibility
    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();
    if (pmoDoc.exists) {
      const pmoData = pmoDoc.data();
      const existingProjectIds = pmoData?.projectIds || [];

      if (!existingProjectIds.includes(projectId)) {
        await pmoDocRef.update({
          projectIds: [...existingProjectIds, projectId],
          updatedAt: new Date()
        });
      }
    }

    console.log(`PMOHierarchical: Successfully created project document ${projectId}`);

    return { success: true };

  } catch (error) {
    console.error(`PMOHierarchical: Error creating project document:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Add a task to a project's tasks subcollection
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID
 * @param taskId - Task ID
 * @param taskData - Task data
 */
export async function addTaskToProject(
  userId: string,
  pmoId: string,
  projectId: string,
  taskId: string,
  taskData: Omit<Task, 'id'>
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`PMOHierarchical: Adding task ${taskId} to project ${projectId} in PMO ${pmoId}`);

    // Ensure project document exists
    const projectDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects')
      .doc(projectId);

    const projectDoc = await projectDocRef.get();
    if (!projectDoc.exists) {
      // Create project document if it doesn't exist
      await projectDocRef.set({
        id: projectId,
        name: `Project ${projectId}`,
        createdAt: new Date(),
        status: 'Active',
        updatedAt: new Date()
      });
    }

    // Add task to project's tasks subcollection
    const taskDocRef = projectDocRef.collection('tasks').doc(taskId);
    await taskDocRef.set({
      ...taskData,
      id: taskId,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Update legacy arrays for backward compatibility
    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();
    if (pmoDoc.exists) {
      const pmoData = pmoDoc.data();
      const existingTaskIds = pmoData?.taskIds || [];

      if (!existingTaskIds.includes(taskId)) {
        await pmoDocRef.update({
          taskIds: [...existingTaskIds, taskId],
          updatedAt: new Date()
        });
      }
    }

    console.log(`PMOHierarchical: Successfully added task ${taskId} to project ${projectId}`);

    return { success: true };

  } catch (error) {
    console.error(`PMOHierarchical: Error adding task to project:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get tasks for a specific project from hierarchical subcollection
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID
 * @param includeTaskData - Whether to include full task data or just IDs
 */
export async function getProjectTasks(
  userId: string,
  pmoId: string,
  projectId: string,
  includeTaskData: boolean = false
): Promise<{
  success: boolean;
  taskIds?: string[];
  tasks?: Task[];
  error?: string;
}> {
  try {
    console.log(`PMOHierarchical: Getting tasks for project ${projectId} in PMO ${pmoId}`);

    const tasksCollectionRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects')
      .doc(projectId)
      .collection('tasks');

    const tasksSnapshot = await tasksCollectionRef.get();

    if (tasksSnapshot.empty) {
      console.log(`PMOHierarchical: No tasks found in subcollection for project ${projectId}, checking legacy structure`);
      return await getLegacyProjectTasks(userId, pmoId, projectId);
    }

    const taskIds = tasksSnapshot.docs.map(doc => doc.id);
    const tasks = includeTaskData
      ? tasksSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Task))
      : undefined;

    console.log(`PMOHierarchical: Found ${taskIds.length} tasks in subcollection for project ${projectId}`);

    return {
      success: true,
      taskIds,
      tasks
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting project tasks:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Fallback function to get project tasks from legacy structure
 */
async function getLegacyProjectTasks(
  userId: string,
  pmoId: string,
  projectId: string
): Promise<{ success: boolean; taskIds?: string[]; error?: string }> {
  try {
    const result = await getLegacyHierarchicalData(userId, pmoId);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || 'Failed to get legacy hierarchical data'
      };
    }

    const projectData = result.data.find(p => p.projectId === projectId);

    if (!projectData) {
      return {
        success: false,
        error: `Project ${projectId} not found in legacy PMO ${pmoId}`
      };
    }

    console.log(`PMOHierarchical: Using legacy structure for project ${projectId} - ${projectData.taskIds.length} tasks`);

    return {
      success: true,
      taskIds: projectData.taskIds
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting legacy project tasks:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Remove a task from a project's subcollection
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID
 * @param taskId - Task ID to remove
 */
export async function removeTaskFromProject(
  userId: string,
  pmoId: string,
  projectId: string,
  taskId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`PMOHierarchical: Removing task ${taskId} from project ${projectId} in PMO ${pmoId}`);

    // Remove task from project's tasks subcollection
    const taskDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects')
      .doc(projectId)
      .collection('tasks')
      .doc(taskId);

    const taskDoc = await taskDocRef.get();
    if (taskDoc.exists) {
      await taskDocRef.delete();
      console.log(`PMOHierarchical: Deleted task ${taskId} from subcollection`);
    } else {
      console.log(`PMOHierarchical: Task ${taskId} not found in subcollection, checking legacy structure`);
    }

    // Also update legacy arrays for backward compatibility
    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();
    if (pmoDoc.exists) {
      const pmoData = pmoDoc.data();
      const existingTaskIds = pmoData?.taskIds || [];
      const updatedLegacyTaskIds = existingTaskIds.filter((id: string) => id !== taskId);

      await pmoDocRef.update({
        taskIds: updatedLegacyTaskIds,
        updatedAt: new Date()
      });
    }

    console.log(`PMOHierarchical: Successfully removed task ${taskId} from project ${projectId}`);

    return { success: true };

  } catch (error) {
    console.error(`PMOHierarchical: Error removing task from project:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get a specific task from a project's subcollection
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID
 * @param taskId - Task ID
 */
export async function getProjectTask(
  userId: string,
  pmoId: string,
  projectId: string,
  taskId: string
): Promise<{ success: boolean; task?: Task; error?: string }> {
  try {
    console.log(`PMOHierarchical: Getting task ${taskId} from project ${projectId} in PMO ${pmoId}`);

    const taskDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .collection('projects')
      .doc(projectId)
      .collection('tasks')
      .doc(taskId);

    const taskDoc = await taskDocRef.get();

    if (!taskDoc.exists) {
      return {
        success: false,
        error: `Task ${taskId} not found in project ${projectId}`
      };
    }

    const task = { id: taskDoc.id, ...taskDoc.data() } as Task;

    return {
      success: true,
      task
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting project task:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get task IDs for a specific project that match the PMO's taskIds array
 * This ensures enhanced notes use actual Firebase task IDs from the correct project
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Specific project ID to get tasks from
 */
export async function getProjectTaskIdsForEnhancedNotes(
  userId: string,
  pmoId: string,
  projectId: string
): Promise<{ success: boolean; taskIds?: string[]; error?: string }> {
  try {
    console.log(`PMOHierarchical: Getting task IDs for enhanced notes - Project: ${projectId}, PMO: ${pmoId}`);

    // Get tasks from the specific project's subcollection
    const projectTasksResult = await getProjectTasks(userId, pmoId, projectId, false);

    if (!projectTasksResult.success || !projectTasksResult.taskIds) {
      console.warn(`PMOHierarchical: No tasks found in project ${projectId} subcollection`);
      return {
        success: false,
        error: `No tasks found in project ${projectId}`
      };
    }

    // Get the PMO document to verify these task IDs are in the legacy array
    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();
    if (!pmoDoc.exists) {
      return {
        success: false,
        error: `PMO document ${pmoId} not found`
      };
    }

    const pmoData = pmoDoc.data();
    const pmoTaskIds = pmoData?.taskIds || [];

    // Filter project task IDs to only include those that exist in PMO's taskIds array
    const validTaskIds = projectTasksResult.taskIds.filter(taskId =>
      pmoTaskIds.includes(taskId)
    );

    console.log(`PMOHierarchical: Found ${validTaskIds.length} valid task IDs for project ${projectId}`);
    console.log(`PMOHierarchical: Valid task IDs: [${validTaskIds.slice(0, 3).join(', ')}${validTaskIds.length > 3 ? '...' : ''}]`);

    return {
      success: true,
      taskIds: validTaskIds
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error getting project task IDs for enhanced notes:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Validate that task metadata matches actual Firebase subcollection structure
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param projectId - Project ID from metadata
 * @param taskId - Task ID from metadata
 * @param taskTitle - Task title for verification
 */
export async function validateTaskMetadata(
  userId: string,
  pmoId: string,
  projectId: string,
  taskId: string,
  taskTitle?: string
): Promise<{
  success: boolean;
  isConsistent: boolean;
  actualLocation?: string;
  error?: string;
}> {
  try {
    console.log(`PMOHierarchical: Validating task metadata - PMO: ${pmoId}, Project: ${projectId}, Task: ${taskId}`);

    // Check if task exists in the specified subcollection location
    const taskResult = await getProjectTask(userId, pmoId, projectId, taskId);

    if (taskResult.success && taskResult.task) {
      // Verify task title matches if provided
      if (taskTitle && taskResult.task.title !== taskTitle) {
        return {
          success: true,
          isConsistent: false,
          actualLocation: `users/${userId}/PMO/${pmoId}/projects/${projectId}/tasks/${taskId}`,
          error: `Task title mismatch: expected "${taskTitle}", found "${taskResult.task.title}"`
        };
      }

      return {
        success: true,
        isConsistent: true,
        actualLocation: `users/${userId}/PMO/${pmoId}/projects/${projectId}/tasks/${taskId}`
      };
    }

    // Task not found in specified location - check if it exists elsewhere
    console.log(`PMOHierarchical: Task ${taskId} not found in specified location, checking other projects`);

    const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);
    if (hierarchicalResult.success && hierarchicalResult.data) {
      for (const project of hierarchicalResult.data) {
        if (project.taskIds.includes(taskId)) {
          return {
            success: true,
            isConsistent: false,
            actualLocation: `users/${userId}/PMO/${pmoId}/projects/${project.projectId}/tasks/${taskId}`,
            error: `Task found in different project: ${project.projectId} instead of ${projectId}`
          };
        }
      }
    }

    return {
      success: true,
      isConsistent: false,
      error: `Task ${taskId} not found in any subcollection for PMO ${pmoId}`
    };

  } catch (error) {
    console.error(`PMOHierarchical: Error validating task metadata:`, error);
    return {
      success: false,
      isConsistent: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
