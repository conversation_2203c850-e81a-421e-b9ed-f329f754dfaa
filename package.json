{"name": "indeftest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next", "dev:memory": "node start-with-memory.js next dev", "start:memory": "node start-with-memory.js next start", "process-docs": "node start-with-memory.js node scripts/process-documents.js"}, "dependencies": {"@11labs/react": "^0.0.4", "@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/openai": "^1.3.16", "@dqbd/tiktoken": "^1.0.17", "@dropzone-ui/react": "^7.0.4", "@emotion/is-prop-valid": "^1.3.1", "@google-cloud/firestore": "^7.10.0", "@google/genai": "^0.9.0", "@google/generative-ai": "^0.24.0", "@heroicons/react": "^2.1.5", "@langchain/anthropic": "^0.3.3", "@langchain/cohere": "^0.2.2", "@langchain/community": "^0.2.33", "@langchain/core": "^0.2.36", "@langchain/groq": "^0.0.17", "@langchain/langgraph": "^0.0.34", "@langchain/openai": "^0.2.7", "@langchain/pinecone": "^0.0.9", "@pinecone-database/pinecone": "^3.0.3", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@react-pdf/renderer": "^3.4.4", "@sendgrid/mail": "^8.1.4", "@sentry/nextjs": "^8.34.0", "@tanstack/react-table": "^8.21.3", "@types/d3": "^7.4.3", "@types/next-auth": "^3.15.0", "@types/webrtc": "^0.0.45", "ai": "^4.3.9", "ajv": "^8.17.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "cohere-ai": "^7.13.0", "cohere-js": "^1.0.26", "d3": "^7.9.0", "d3-dsv": "^2.0.0", "daisyui": "^4.12.10", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "elevenlabs": "^1.54.0", "firebase": "^10.14.1", "firebase-admin": "^12.3.1", "framer-motion": "^12.5.0", "gpt-3-encoder": "^1.1.4", "gpt-tokenizer": "^2.5.1", "groq-sdk": "^0.5.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "langchain": "^0.3.0", "lodash": "^4.17.21", "lucide-react": "^0.428.0", "mammoth": "^1.8.0", "next": "^15.0.0", "next-auth": "^4.24.8", "node-latest-version": "^1.0.16", "node-polyfill-webpack-plugin": "^4.0.0", "nodemailer": "^6.9.15", "omni-tokenizer": "^2.0.1", "p-limit": "^6.1.0", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.6.82", "puppeteer": "^24.6.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-feather": "^2.0.10", "react-firebase-hooks": "^5.1.1", "react-flow-renderer": "^10.3.17", "react-markdown": "^9.1.0", "react-pdf": "^9.1.0", "react-redux": "^9.1.2", "react-router-dom": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "react-syntax-highlighter-prismjs": "^5.9.0", "reactflow": "^11.11.4", "recharts": "^2.13.3", "rehype-raw": "^7.0.0", "tailwind-merge": "^3.2.0", "util": "^0.12.5", "uuid": "^10.0.0", "web-streams-polyfill": "^4.0.0", "xlsx": "^0.18.5", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.2"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/marked": "^6.0.0", "@types/node": "^20.16.12", "@types/nodemailer": "^6.4.16", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/react": "^18.3.23", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/recharts": "^1.8.29", "file-loader": "^6.2.0", "null-loader": "^4.0.1", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5.6.3"}}