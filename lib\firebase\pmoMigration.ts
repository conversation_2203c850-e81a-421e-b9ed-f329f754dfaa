// lib/firebase/pmoMigration.ts
import { adminDb } from '../../components/firebase-admin';
import { PMOProjectTaskMapping } from '../agents/pmo/PMOInterfaces';
import { Task } from '../../admin/planner/types';
import { createProjectDocument, addTaskToProject } from './pmoHierarchical';

/**
 * Migration utilities to convert existing flat PMO structure to hierarchical
 */

export interface MigrationResult {
  success: boolean;
  migratedPMOs: number;
  errors: string[];
  details: {
    pmoId: string;
    projectCount: number;
    taskCount: number;
    status: 'success' | 'error' | 'skipped';
    error?: string;
  }[];
}

/**
 * Migrate a single PMO record from flat to hierarchical structure
 * @param userId - User email
 * @param pmoId - PMO document ID
 * @param dryRun - If true, only simulate the migration without making changes
 */
export async function migratePMOToHierarchical(
  userId: string,
  pmoId: string,
  dryRun: boolean = false
): Promise<{
  success: boolean;
  error?: string;
  details?: {
    projectCount: number;
    taskCount: number;
    mapping: PMOProjectTaskMapping;
  };
}> {
  try {
    console.log(`PMOMigration: ${dryRun ? 'Simulating' : 'Executing'} migration for PMO ${pmoId}`);

    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();

    if (!pmoDoc.exists) {
      return {
        success: false,
        error: `PMO document ${pmoId} not found`
      };
    }

    const pmoData = pmoDoc.data();

    // Check if already migrated
    if (pmoData?.projectTaskMapping) {
      console.log(`PMOMigration: PMO ${pmoId} already has hierarchical structure`);
      return {
        success: true,
        details: {
          projectCount: Object.keys(pmoData.projectTaskMapping).length,
          taskCount: Object.values(pmoData.projectTaskMapping).reduce(
            (total: number, project: any) => total + (project.taskIds?.length || 0),
            0
          ),
          mapping: pmoData.projectTaskMapping
        }
      };
    }

    const projectIds = pmoData?.projectIds || [];
    const taskIds = pmoData?.taskIds || [];

    if (projectIds.length === 0) {
      console.log(`PMOMigration: PMO ${pmoId} has no projects to migrate`);
      return {
        success: true,
        details: {
          projectCount: 0,
          taskCount: 0,
          mapping: {}
        }
      };
    }

    // Create hierarchical subcollection structure
    const projectTaskMapping: PMOProjectTaskMapping = {};
    const migratedTasksCount = { total: 0, successful: 0, failed: 0 };

    // Strategy: Query actual tasks to find their project relationships
    for (const projectId of projectIds) {
      // Try to get project information from Firebase
      let projectInfo = {
        id: projectId,
        name: `Project ${projectId}`,
        createdAt: new Date(),
        status: 'Active'
      };

      try {
        // Attempt to get actual project data
        const projectDoc = await adminDb
          .collection('projects')
          .doc(projectId)
          .get();

        if (projectDoc.exists) {
          const projectData = projectDoc.data();
          projectInfo = {
            id: projectId,
            name: projectData?.name || `Project ${projectId}`,
            createdAt: projectData?.createdAt?.toDate() || new Date(),
            status: projectData?.status || 'Active'
          };
        }
      } catch (error) {
        console.warn(`PMOMigration: Could not fetch project data for ${projectId}:`, error);
      }

      // Create project document in hierarchical structure
      if (!dryRun) {
        const projectResult = await createProjectDocument(userId, pmoId, projectId, projectInfo);
        if (!projectResult.success) {
          console.warn(`PMOMigration: Failed to create project document for ${projectId}: ${projectResult.error}`);
        }
      }

      // Find tasks that belong to this project
      const projectTaskIds: string[] = [];

      try {
        // Query global tasks collection to find tasks for this project
        const tasksQuery = adminDb
          .collection('tasks')
          .where('projectId', '==', projectId);

        const tasksSnapshot = await tasksQuery.get();

        for (const taskDoc of tasksSnapshot.docs) {
          const taskId = taskDoc.id;
          const taskData = taskDoc.data() as Task;

          projectTaskIds.push(taskId);
          migratedTasksCount.total++;

          if (!dryRun) {
            // Migrate task to hierarchical subcollection
            try {
              const taskResult = await addTaskToProject(userId, pmoId, projectId, taskId, taskData);
              if (taskResult.success) {
                migratedTasksCount.successful++;
                console.log(`PMOMigration: Migrated task ${taskId} to project ${projectId} subcollection`);
              } else {
                migratedTasksCount.failed++;
                console.warn(`PMOMigration: Failed to migrate task ${taskId}: ${taskResult.error}`);
              }
            } catch (taskError) {
              migratedTasksCount.failed++;
              console.error(`PMOMigration: Error migrating task ${taskId}:`, taskError);
            }
          }
        }

        console.log(`PMOMigration: Found ${projectTaskIds.length} tasks for project ${projectId}`);

      } catch (error) {
        console.warn(`PMOMigration: Error querying tasks for project ${projectId}:`, error);
      }

      projectTaskMapping[projectId] = {
        projectInfo,
        taskIds: projectTaskIds
      };
    }

    console.log(`PMOMigration: Task migration summary - Total: ${migratedTasksCount.total}, Successful: ${migratedTasksCount.successful}, Failed: ${migratedTasksCount.failed}`);

    const migrationDetails = {
      projectCount: projectIds.length,
      taskCount: migratedTasksCount.total,
      tasksMigrated: migratedTasksCount.successful,
      tasksFailed: migratedTasksCount.failed,
      mapping: projectTaskMapping
    };

    if (dryRun) {
      console.log(`PMOMigration: Dry run completed for PMO ${pmoId}:`, migrationDetails);
      return {
        success: true,
        details: migrationDetails
      };
    }

    // Execute the migration
    await pmoDocRef.update({
      projectTaskMapping,
      migrationDate: new Date(),
      migrationVersion: '1.0',
      updatedAt: new Date()
    });

    console.log(`PMOMigration: Successfully migrated PMO ${pmoId} to hierarchical structure`);

    return {
      success: true,
      details: migrationDetails
    };

  } catch (error) {
    console.error(`PMOMigration: Error migrating PMO ${pmoId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Migrate all PMO records for a user from flat to hierarchical structure
 * @param userId - User email
 * @param dryRun - If true, only simulate the migration without making changes
 */
export async function migrateAllPMOsToHierarchical(
  userId: string,
  dryRun: boolean = false
): Promise<MigrationResult> {
  try {
    console.log(`PMOMigration: ${dryRun ? 'Simulating' : 'Executing'} migration for all PMOs for user ${userId}`);

    const pmoCollection = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO');

    const pmoSnapshot = await pmoCollection.get();

    if (pmoSnapshot.empty) {
      return {
        success: true,
        migratedPMOs: 0,
        errors: [],
        details: []
      };
    }

    const migrationResult: MigrationResult = {
      success: true,
      migratedPMOs: 0,
      errors: [],
      details: []
    };

    for (const pmoDoc of pmoSnapshot.docs) {
      const pmoId = pmoDoc.id;

      try {
        const result = await migratePMOToHierarchical(userId, pmoId, dryRun);

        if (result.success) {
          migrationResult.migratedPMOs++;
          migrationResult.details.push({
            pmoId,
            projectCount: result.details?.projectCount || 0,
            taskCount: result.details?.taskCount || 0,
            status: 'success'
          });
        } else {
          migrationResult.errors.push(`PMO ${pmoId}: ${result.error}`);
          migrationResult.details.push({
            pmoId,
            projectCount: 0,
            taskCount: 0,
            status: 'error',
            error: result.error
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        migrationResult.errors.push(`PMO ${pmoId}: ${errorMessage}`);
        migrationResult.details.push({
          pmoId,
          projectCount: 0,
          taskCount: 0,
          status: 'error',
          error: errorMessage
        });
      }
    }

    if (migrationResult.errors.length > 0) {
      migrationResult.success = false;
    }

    console.log(`PMOMigration: Migration completed. Success: ${migrationResult.success}, Migrated: ${migrationResult.migratedPMOs}, Errors: ${migrationResult.errors.length}`);

    return migrationResult;

  } catch (error) {
    console.error(`PMOMigration: Error during bulk migration:`, error);
    return {
      success: false,
      migratedPMOs: 0,
      errors: [error instanceof Error ? error.message : String(error)],
      details: []
    };
  }
}

/**
 * Validate the hierarchical structure of a PMO record
 * @param userId - User email
 * @param pmoId - PMO document ID
 */
export async function validateHierarchicalStructure(
  userId: string,
  pmoId: string
): Promise<{
  isValid: boolean;
  issues: string[];
  details: {
    hasHierarchicalStructure: boolean;
    hasLegacyStructure: boolean;
    projectCount: number;
    taskCount: number;
    orphanedTasks: string[];
  };
}> {
  try {
    const pmoDoc = await adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId)
      .get();

    if (!pmoDoc.exists) {
      return {
        isValid: false,
        issues: [`PMO document ${pmoId} not found`],
        details: {
          hasHierarchicalStructure: false,
          hasLegacyStructure: false,
          projectCount: 0,
          taskCount: 0,
          orphanedTasks: []
        }
      };
    }

    const pmoData = pmoDoc.data();
    const issues: string[] = [];

    const hasHierarchicalStructure = !!(pmoData?.projectTaskMapping);
    const hasLegacyStructure = !!(pmoData?.projectIds || pmoData?.taskIds);

    let projectCount = 0;
    let taskCount = 0;
    let orphanedTasks: string[] = [];

    // Check for hierarchical subcollection structure
    try {
      const projectsCollection = adminDb
        .collection('users')
        .doc(userId)
        .collection('PMO')
        .doc(pmoId)
        .collection('projects');

      const projectsSnapshot = await projectsCollection.get();

      if (!projectsSnapshot.empty) {
        hasHierarchicalStructure = true;
        projectCount = projectsSnapshot.docs.length;

        for (const projectDoc of projectsSnapshot.docs) {
          const projectId = projectDoc.id;

          // Check if project has tasks subcollection
          const tasksCollection = projectDoc.ref.collection('tasks');
          const tasksSnapshot = await tasksCollection.get();

          taskCount += tasksSnapshot.docs.length;

          console.log(`PMOValidation: Project ${projectId} has ${tasksSnapshot.docs.length} tasks in subcollection`);
        }

        console.log(`PMOValidation: Found hierarchical subcollection structure with ${projectCount} projects and ${taskCount} tasks`);
      } else {
        // Check legacy projectTaskMapping structure
        if (pmoData?.projectTaskMapping) {
          hasHierarchicalStructure = true;
          const mapping = pmoData.projectTaskMapping as PMOProjectTaskMapping;
          projectCount = Object.keys(mapping).length;

          for (const [projectId, projectData] of Object.entries(mapping)) {
            if (!projectData.taskIds || !Array.isArray(projectData.taskIds)) {
              issues.push(`Project ${projectId} has invalid taskIds structure in mapping`);
            } else {
              taskCount += projectData.taskIds.length;
            }

            if (!projectData.projectInfo) {
              issues.push(`Project ${projectId} missing projectInfo in mapping`);
            }
          }

          console.log(`PMOValidation: Found legacy hierarchical mapping structure`);
        }
      }
    } catch (error) {
      issues.push(`Error checking hierarchical subcollection structure: ${error instanceof Error ? error.message : String(error)}`);
    }

    if (hasLegacyStructure && hasHierarchicalStructure) {
      // Check for consistency between legacy and hierarchical structures
      const legacyProjectIds = pmoData.projectIds || [];
      const legacyTaskIds = pmoData.taskIds || [];
      const hierarchicalProjectIds = Object.keys(pmoData.projectTaskMapping || {});

      const allHierarchicalTaskIds = Object.values(pmoData.projectTaskMapping || {})
        .flatMap((project: any) => project.taskIds || []);

      // Find orphaned tasks (in legacy but not in hierarchical)
      orphanedTasks = legacyTaskIds.filter((taskId: string) =>
        !allHierarchicalTaskIds.includes(taskId)
      );

      if (orphanedTasks.length > 0) {
        issues.push(`Found ${orphanedTasks.length} orphaned tasks in legacy structure`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      details: {
        hasHierarchicalStructure,
        hasLegacyStructure,
        projectCount,
        taskCount,
        orphanedTasks
      }
    };

  } catch (error) {
    return {
      isValid: false,
      issues: [error instanceof Error ? error.message : String(error)],
      details: {
        hasHierarchicalStructure: false,
        hasLegacyStructure: false,
        projectCount: 0,
        taskCount: 0,
        orphanedTasks: []
      }
    };
  }
}
