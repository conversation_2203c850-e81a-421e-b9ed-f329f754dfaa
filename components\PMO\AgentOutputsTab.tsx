'use client';

import React,  { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../app/context/AuthContext';
// Assuming AgentOutput might have base fields. The key is how ExtendedAgentOutput defines/overrides them.
import { AgentOutput, getAgentOutputs } from '../../lib/firebase/agentOutputs';
import MarkdownRenderer from '../MarkdownRenderer';
import AgentOutputModal from '../marketing/AgentOutputModal';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import { Clock, Download, FileText, Search, X, Play, Loader2, User, Tag, Settings } from 'lucide-react';
import { Input } from '../ui/input';
import { toast } from '../ui/use-toast';
import ProjectCreationReviewModal from './ProjectCreationReviewModal';

// Define TimestampType for clarity and consistency
type FirestoreTimestampRepresentation = {
  seconds: number;
  nanoseconds: number;
  toDate?: () => Date; // toDate is a method on the actual Firebase Timestamp object
} | {
  _seconds: number; // Sometimes serialized with underscores
  _nanoseconds: number;
  toDate?: () => Date;
};
type TimestampType = string | number | Date | FirestoreTimestampRepresentation | null | undefined;

// Extended interface
// Omit 'createdAt' from the base AgentOutput if it exists and we want to redefine it with TimestampType
// or if its type is incompatible.
interface ExtendedAgentOutput extends Omit<AgentOutput, 'createdAt'> {
  // Fields from the original problem's ExtendedAgentOutput definition
  sources?: Array<{
    title: string;
    doc_id: string;
    page?: number;
    relevance?: number;
  }>;
  result?: {
    output?: string;
    content?: string;
    thinking?: string;
  } | string;
  prompt?: string;
  isCollaboration?: boolean;
  agentMessages?: {
    from: string;
    to: string;
    message: string;
  }[];

  // Additional fields or refinements for ExtendedAgentOutput
  createdAt?: TimestampType; // Explicitly defined here, potentially overriding AgentOutput['createdAt']
  metadata?: {
    recordTitle?: string;
    projectTitle?: string;
    title?: string;
    category?: string;
    pmoId?: string;
    assignedTeam?: string;
    teamName?: string;
    assignedTeamId?: string;
    teamId?: string;
    recordDescription?: string;
    teamSelectionRationale?: string;
    recordPriority?: string;
    [key: string]: any;
  };

  // Ensure timestamp in ExtendedAgentOutput uses our TimestampType.
  // This overrides or defines 'timestamp' from AgentOutput.
  timestamp: TimestampType;

  // Add modelProvider and modelName as optional properties
  modelProvider?: string;
  modelName?: string;

  // modelInfo might still exist on the base AgentOutput, or be added by API results.
  // If it's not on base AgentOutput and is expected, it should be defined here too.
  // For now, focusing on modelProvider and modelName as per the error.
  modelInfo?: any; // Or a more specific type if known. If AgentOutput defines it, this might refine it.
                    // This was present in previous solution passing modelInfo={selectedOutput?.modelInfo}
                    // Retaining it as it might be part of some data sources.

  // QA Analysis Results field (can be at root level or in agentInteractions)
  qaAnalysisResult?: {
    conversationId?: string;
    questions?: Array<{
      question?: string;
      answer?: {
        question?: string;
        answer?: string;
        conversationId?: string;
      } | string;
    }>;
  };

  // Agent interactions field (from research agent collaboration)
  agentInteractions?: {
    documentQueries?: any[];
    questionAnswers?: any[];
    pmoAssessmentAnalysis?: string;
    qaAnalysisResult?: {
      conversationId?: string;
      questions?: Array<{
        question?: string;
        answer?: {
          question?: string;
          answer?: string;
          conversationId?: string;
        } | string;
      }>;
    };
  };
}

// For the API response structure from /api/agent-outputs
interface ApiGlobalOutputsResponse {
  results: ExtendedAgentOutput[];
  hasMore: boolean;
  lastTimestamp: string | null;
}


/**
 * PMO Agent Outputs Tab Component
 *
 * Displays a list of PMO agent outputs with filtering and viewing capabilities.
 */
export default function AgentOutputsTab() {
  const { user } = useAuth();
  const [outputs, setOutputs] = useState<ExtendedAgentOutput[]>([]);
  const [selectedOutput, setSelectedOutput] = useState<ExtendedAgentOutput | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreatingStrategicPlan, setIsCreatingStrategicPlan] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState<boolean>(false);

  // Modal state
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    content: string;
    contentType: 'output' | 'thinking' | 'collaboration' | 'query-documents' | 'qa-analysis';
  } | null>(null);

  // Project creation review modal state
  const [projectReviewModalOpen, setProjectReviewModalOpen] = useState(false);
  const [projectReviewData, setProjectReviewData] = useState<{
    project: any;
    tasks: any[];
    requestId: string;
    pmoId?: string;
  } | null>(null);
  const [isLoadingProjectPreview, setIsLoadingProjectPreview] = useState(false);
  const [isCommittingProject, setIsCommittingProject] = useState(false);

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [lastTimestamp, setLastTimestamp] = useState<string | null>(null);
  const ITEMS_PER_PAGE = 15;

  // Fetch agent outputs with pagination
  const fetchOutputs = useCallback(async (resetPage = true) => {
    if (!user?.email) return;

    if (resetPage) {
      setPage(1);
      setLastTimestamp(null);
      setLoading(true);
      setHasMore(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const currentPage = resetPage ? 1 : page;
      let url = `/api/agent-outputs?page=${currentPage}&limit=${ITEMS_PER_PAGE}`;

      if (!resetPage && lastTimestamp) {
        url += `&lastTimestamp=${encodeURIComponent(lastTimestamp)}`;
      }

      const [userSpecificOutputsResults, globalOutputsResponse] = await Promise.all([
        Promise.all([
          getAgentOutputs(user.email, 'PMO'),
          getAgentOutputs(user.email, 'PMO_Assessment_And_Requirements'),
          getAgentOutputs(user.email, 'Marketing'),
          getAgentOutputs(user.email, 'Research'),
          getAgentOutputs(user.email, 'SoftwareDesign'),
          getAgentOutputs(user.email, 'Sales'),
          getAgentOutputs(user.email, 'BusinessAnalysis')
        ]),
        fetch(url)
      ]);

      const userOutputs: AgentOutput[] = userSpecificOutputsResults.flat();

      let globalOutputs: ExtendedAgentOutput[] = [];
      let globalHasMore = false;
      let globalLastTimestamp: string | null = null;

      if (globalOutputsResponse.ok) {
        const globalData = (await globalOutputsResponse.json()) as ApiGlobalOutputsResponse;
        globalOutputs = globalData.results || [];
        globalHasMore = globalData.hasMore || false;
        globalLastTimestamp = globalData.lastTimestamp || null;
      } else {
        console.warn('Failed to fetch global agent outputs, using only user-specific outputs');
      }

      // Process the outputs to identify collaboration outputs
      const processedUserOutputs = userOutputs.map((output: AgentOutput) => {
        // Check if this is a collaboration output (Strategic Analysis)
        const isCollaboration = Boolean(
          ((output as any).agentMessages && (output as any).agentMessages.length > 0) ||
          (typeof (output as any).result === 'object' && (output as any).result?.output &&
           typeof (output as any).result.output === 'string' &&
           (output as any).result.output.includes('Strategic Analysis'))
        );

        return {
          ...output,
          isCollaboration
        } as ExtendedAgentOutput;
      });

      const processedGlobalOutputs = globalOutputs.map((output: ExtendedAgentOutput) => {
        // Check if this is a collaboration output (Strategic Analysis)
        const isCollaboration = Boolean(
          (output.agentMessages && output.agentMessages.length > 0) ||
          (typeof output.result === 'object' && output.result?.output &&
           typeof output.result.output === 'string' &&
           output.result.output.includes('Strategic Analysis'))
        );

        return {
          ...output,
          isCollaboration
        };
      });

      const newOutputs: ExtendedAgentOutput[] = [
        ...processedUserOutputs,
        ...processedGlobalOutputs
      ];

      if (resetPage) {
        setOutputs(newOutputs);
      } else {
        setOutputs(prev => [...prev, ...newOutputs]);
      }

      if (globalLastTimestamp) {
        setLastTimestamp(globalLastTimestamp);
      }
      setHasMore(globalHasMore);

      if (!resetPage) {
        setPage(currentPage + 1);
      }

      setError(null);
    } catch (err: any) {
      console.error('Error fetching PMO-related agent outputs:', err);
      setError(err.message || 'Failed to fetch PMO-related agent outputs');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [user, page, lastTimestamp]); // ITEMS_PER_PAGE is constant, removed from deps


  const loadMoreOutputs = () => {
    if (!loading && !loadingMore && hasMore) {
      fetchOutputs(false);
    }
  };

  useEffect(() => {
    if (user?.email) {
      fetchOutputs(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.email]);

  const getOutputContent = (output: ExtendedAgentOutput): string => {
    if (output.content) return output.content;
    const result = output.result;
    if (typeof result === 'string') return result;
    return result?.output || result?.content || '';
  };

  const getAgentTypeInfo = (agentType: string | undefined) => { // Added undefined check for agentType
    const type = agentType || 'Unknown';
    switch (type) {
      case 'PMO':
      case 'PMO_Assessment_And_Requirements':
        return { name: 'PMO', color: 'bg-purple-600', team: 'PMO Team' };
      case 'Marketing':
        return { name: 'Marketing', color: 'bg-blue-600', team: 'Marketing Team' };
      case 'strategic-director':
        return { name: 'Marketing Strategy', color: 'bg-blue-600', team: 'Marketing Team' };
      case 'Research':
        return { name: 'Research', color: 'bg-green-600', team: 'Research Team' };
      case 'research-team':
        return { name: 'Research Analysis', color: 'bg-green-600', team: 'Research Team' };
      case 'research-strategic-director':
        return { name: 'Research Strategy', color: 'bg-green-600', team: 'Research Team' };
      case 'ResearchAgentManager':
        return { name: 'Research Strategy', color: 'bg-green-600', team: 'Research Team' };
      case 'SoftwareDesign':
        return { name: 'Software Design', color: 'bg-orange-600', team: 'Software Design Team' };
      case 'Sales':
        return { name: 'Sales', color: 'bg-red-600', team: 'Sales Team' };
      case 'BusinessAnalysis':
        return { name: 'Business Analysis', color: 'bg-yellow-600', team: 'Business Analysis Team' };
      default:
        return { name: type, color: 'bg-gray-600', team: 'Unknown Team' };
    }
  };

  const formatTimestamp = (timestampInput: TimestampType): string => {
    if (!timestampInput) return 'Unknown date';

    let date: Date;

    try {
      if (timestampInput instanceof Date) {
        date = timestampInput;
      } else if (typeof timestampInput === 'string' || typeof timestampInput === 'number') {
        date = new Date(timestampInput);
      } else if (typeof timestampInput === 'object' && timestampInput !== null) {
        // Check for empty object first
        if (Object.keys(timestampInput).length === 0) {
          console.warn('Empty timestamp object received, using current date');
          date = new Date();
        } else if ('toDate' in timestampInput && typeof timestampInput.toDate === 'function') {
          date = timestampInput.toDate();
        } else if ('seconds' in timestampInput && typeof timestampInput.seconds === 'number') {
          date = new Date(timestampInput.seconds * 1000);
        } else if ('_seconds' in timestampInput && typeof (timestampInput as any)._seconds === 'number') {
          date = new Date((timestampInput as any)._seconds * 1000);
        } else {
          console.warn('Unrecognized timestamp object format:', timestampInput, 'using current date');
          date = new Date();
        }
      } else {
        console.warn('Invalid timestamp format:', timestampInput, 'using current date');
        date = new Date();
      }

      // Validate the resulting date
      if (isNaN(date.getTime())) {
        console.warn('Invalid date computed from timestamp:', timestampInput, 'using current date');
        date = new Date();
      }

      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const month = months[date.getMonth()];
      const day = date.getDate();
      const year = date.getFullYear();
      let hours = date.getHours();
      const ampm = hours >= 12 ? 'p.m.' : 'a.m.';
      hours = hours % 12;
      hours = hours ? hours : 12;
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${month} ${day}, ${year}, ${hours}:${minutes} ${ampm}`;

    } catch (error) {
      console.warn('Error formatting timestamp:', timestampInput, error, 'using current date');
      const now = new Date();
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const month = months[now.getMonth()];
      const day = now.getDate();
      const year = now.getFullYear();
      let hours = now.getHours();
      const ampm = hours >= 12 ? 'p.m.' : 'a.m.';
      hours = hours % 12;
      hours = hours ? hours : 12;
      const minutes = now.getMinutes().toString().padStart(2, '0');
      return `${month} ${day}, ${year}, ${hours}:${minutes} ${ampm}`;
    }
  };

  const getOutputTitle = (output: ExtendedAgentOutput): string => {
    if (output.title && output.title !== 'Agent Output' && output.title !== 'Untitled') {
      return output.title;
    }
    if (output.metadata?.recordTitle) return output.metadata.recordTitle;
    if (output.metadata?.projectTitle) return output.metadata.projectTitle;
    if (output.metadata?.title) return output.metadata.title;
    const category = output.category || output.metadata?.category;
    if (category && category.includes('PMO -')) {
      const parts = category.split(' - ');
      if (parts.length >= 2) return parts[1];
    }
    if (output.prompt) {
      let promptTitle = output.prompt.substring(0, 80).trim();
      promptTitle = promptTitle.replace(/^(analyze|create|generate|develop|build|design)\s+/i, '');
      promptTitle = promptTitle.charAt(0).toUpperCase() + promptTitle.slice(1);
      return promptTitle;
    }
    const content = getOutputContent(output);
    if (content) {
      const firstLine = content.split('\n')[0].trim();
      if (firstLine && firstLine.length > 10 && firstLine.length < 100) {
        const cleanLine = firstLine.replace(/^#+\s*/, '').trim();
        if (cleanLine) return cleanLine;
      }
    }
    return `${getAgentTypeInfo(output.agentType).name} Output`;
  };

  const filteredOutputs = outputs.filter(output => {
    // First apply team-based filtering
    const agentInfo = getAgentTypeInfo(output.agentType);
    const teamName = output.metadata?.teamName || agentInfo.team;
    const teamId = output.metadata?.teamId || output.metadata?.assignedTeamId;

    // EXCLUDE outputs where team name or team ID is exactly 'Research'
    // INCLUDE outputs where team name or team ID is 'Research Strategy'
    const isBasicResearch = (
      teamName === 'Research Team' && agentInfo.name === 'Research'
    ) || (
      teamId === 'Research' && agentInfo.name === 'Research'
    ) || (
      output.agentType === 'Research' && agentInfo.name === 'Research'
    );

    const isResearchStrategy = (
      agentInfo.name === 'Research Strategy' ||
      teamName === 'Research Strategy' ||
      teamId === 'Research Strategy' ||
      output.agentType === 'ResearchAgentManager' ||
      output.agentType === 'research-strategic-director'
    );

    // Exclude basic Research outputs, but include Research Strategy outputs
    if (isBasicResearch && !isResearchStrategy) {
      return false;
    }

    // Then apply search term filtering
    const title = getOutputTitle(output);
    const content = getOutputContent(output);
    const searchLower = searchTerm.toLowerCase();
    return title.toLowerCase().includes(searchLower) || content.toLowerCase().includes(searchLower);
  });

  const handleOutputSelect = (output: ExtendedAgentOutput) => {
    setSelectedOutput(output);
  };

  const openOutputModal = (title: string, content: string, contentType: 'output' | 'thinking' | 'collaboration' | 'query-documents' | 'qa-analysis') => {
    setModalContent({ title, content, contentType });
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setModalContent(null);
  };

  const getQueryDocumentsContent = (output: ExtendedAgentOutput): string => {
    if (output.sources && output.sources.length > 0) {
      let content = "Found the following information:\n\n";
      content += "**• Directly relevant information:**\n\n";
      output.sources.forEach((source, index) => {
        content += `${index + 1}. **${source.title}**`;
        if (source.page) content += ` (Page ${source.page})`;
        if (source.relevance) content += ` - Relevance: ${Math.round(source.relevance * 100)}%`;
        content += "\n";
      });
      const mainContent = getOutputContent(output);
      if (mainContent) {
        content += "\n\n**Query Results:**\n\n" + mainContent;
      }
      return content;
    }
    return "";
  };

  // Helper function to get QA analysis result from either location
  const getQAAnalysisResult = (output: ExtendedAgentOutput) => {
    // Check direct qaAnalysisResult field first
    if (output.qaAnalysisResult && output.qaAnalysisResult.questions && output.qaAnalysisResult.questions.length > 0) {
      return output.qaAnalysisResult;
    }

    // Check agentInteractions.qaAnalysisResult field
    if (output.agentInteractions?.qaAnalysisResult &&
        output.agentInteractions.qaAnalysisResult.questions &&
        output.agentInteractions.qaAnalysisResult.questions.length > 0) {
      return output.agentInteractions.qaAnalysisResult;
    }

    return null;
  };

  const getQAAnalysisContent = (output: ExtendedAgentOutput): string => {
    const qaResult = getQAAnalysisResult(output);

    if (qaResult) {
      let content = "# QA Analysis Results\n\n";
      content += "Strategic analysis questions and answers extracted from the agent output.\n\n";

      qaResult.questions!.forEach((qa: any, index: number) => {
        content += `## Question ${index + 1}\n\n`;
        content += `**Q:** ${qa.question || qa.answer?.question || 'Question not available'}\n\n`;
        content += `**A:** ${qa.answer?.answer || qa.answer || 'Answer not available'}\n\n`;

        if (qa.answer?.conversationId) {
          content += `*Conversation ID: ${qa.answer.conversationId}*\n\n`;
        }

        content += "---\n\n";
      });

      if (qaResult.conversationId) {
        content += `**Analysis Session:** ${qaResult.conversationId}\n\n`;
      }

      return content;
    }
    return "No QA analysis results found.";
  };

  const isPMOFormatCategory = (category: string | undefined): boolean => {
    if (!category) return false;
    const pmoPattern = /^PMO\s*-\s*.+\s*-\s*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return pmoPattern.test(category);
  };

  const getPMOCategory = (output: ExtendedAgentOutput, pmoId: string): string => {
    if (output.metadata?.category && isPMOFormatCategory(output.metadata.category)) {
      return output.metadata.category;
    }
    return `PMO - ${getOutputTitle(output)} - ${pmoId}`;
  };

  const triggerStrategicPlan = async (output: ExtendedAgentOutput) => {
    if (isCreatingStrategicPlan || !user?.email) return;
    const pmoId = output.metadata?.pmoId;
    const teamName = output.metadata?.assignedTeam || output.metadata?.teamName;
    const teamId = output.metadata?.assignedTeamId || output.metadata?.teamId;
    if (!pmoId || !teamName) {
      toast({
        title: "Cannot Create Strategic Plan",
        description: "Missing PMO ID or team information in the selected output.",
        variant: "destructive"
      });
      return;
    }
    setIsCreatingStrategicPlan(true);
    try {
      const categoryToUse = getPMOCategory(output, pmoId);
      const response = await fetch('/api/pmo-trigger-strategic-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pmoId,
          teamId,
          teamName,
          projectTitle: output.metadata?.recordTitle || getOutputTitle(output),
          projectDescription: output.metadata?.recordDescription || '',
          pmoAssessment: getOutputContent(output),
          teamSelectionRationale: output.metadata?.teamSelectionRationale || '',
          priority: output.metadata?.recordPriority || 'Medium',
          category: categoryToUse,
          userId: user.email,
          requirementsDocument: getOutputContent(output)
        }),
      });
      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create strategic plan');
      }
      toast({
        title: "Strategic Plan Created",
        description: `${teamName} team has successfully created their strategic implementation plan.`,
      });
      fetchOutputs(true);
    } catch (error: any) {
      console.error('Error creating strategic plan:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create strategic plan. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingStrategicPlan(false);
    }
  };

  const triggerProjectCreationReview = async (output: ExtendedAgentOutput) => {
    if (isLoadingProjectPreview || !user?.email) return;

    const pmoId = output.metadata?.pmoId;
    if (!pmoId) {
      toast({
        title: "Cannot Create Project",
        description: "Missing PMO ID in the selected output.",
        variant: "destructive"
      });
      return;
    }

    setIsLoadingProjectPreview(true);
    try {
      const response = await fetch('/api/project-creation-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requestId: output.id,
          pmoId,
          userId: user.email
        }),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to preview project creation');
      }

      setProjectReviewData({
        project: result.data.project,
        tasks: result.data.tasks,
        requestId: output.id,
        pmoId
      });
      setProjectReviewModalOpen(true);

    } catch (error: any) {
      console.error('Error previewing project creation:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to preview project creation. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingProjectPreview(false);
    }
  };

  const handleProjectCommit = async (approvedTasks: any[]) => {
    if (!projectReviewData || !user?.email) return;

    setIsCommittingProject(true);
    try {
      const response = await fetch('/api/project-creation-commit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project: projectReviewData.project,
          approvedTasks,
          requestId: projectReviewData.requestId,
          pmoId: projectReviewData.pmoId,
          userId: user.email
        }),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create project');
      }

      toast({
        title: "Project Created Successfully",
        description: `Created "${result.data.projectName}" with ${result.data.tasksCreated} tasks and ${result.data.subtasksCreated} subtasks.`,
      });

      setProjectReviewModalOpen(false);
      setProjectReviewData(null);
      fetchOutputs(true);

    } catch (error: any) {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create project. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCommittingProject(false);
    }
  };

  const getOutputTimestamp = (output: ExtendedAgentOutput): TimestampType => {
    // Helper function to check if a timestamp value is valid
    const isValidTimestamp = (ts: any): boolean => {
      if (!ts) return false;
      if (typeof ts === 'string' || typeof ts === 'number') return true;
      if (ts instanceof Date) return !isNaN(ts.getTime());
      if (typeof ts === 'object' && ts !== null) {
        // Check for Firestore timestamp properties
        if ('seconds' in ts && typeof ts.seconds === 'number') return true;
        if ('_seconds' in ts && typeof ts._seconds === 'number') return true;
        if ('toDate' in ts && typeof ts.toDate === 'function') return true;
        // Reject empty objects
        if (Object.keys(ts).length === 0) return false;
      }
      return false;
    };

    // Try createdAt first, then timestamp, then fallback to current date
    if (isValidTimestamp(output.createdAt)) {
      return output.createdAt;
    }
    if (isValidTimestamp(output.timestamp)) {
      return output.timestamp;
    }

    // Fallback to current date if no valid timestamp found
    console.warn('No valid timestamp found for output:', output.id, 'using current date');
    return new Date();
  };

  if (loading && outputs.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">PMO Agent Outputs</h2>
          <Skeleton className="h-10 w-64" />
        </div>
        {[1, 2, 3].map(i => (
          <Skeleton key={i} className="h-40 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-800 rounded-md text-red-800 dark:text-red-300">
        <h2 className="text-lg font-semibold mb-2">Error Loading Outputs</h2>
        <p>{error}</p>
        <Button variant="outline" className="mt-4" onClick={() => fetchOutputs(true)}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {modalOpen && modalContent && (
        <AgentOutputModal
          isOpen={modalOpen}
          onClose={closeModal}
          title={modalContent.title}
          content={modalContent.content}
          contentType={modalContent.contentType as 'output' | 'thinking' | 'collaboration' | 'query-documents' | 'qa-analysis'}
          timestamp={selectedOutput ? formatTimestamp(getOutputTimestamp(selectedOutput)) : undefined}
        />
      )}

      {projectReviewModalOpen && projectReviewData && (
        <ProjectCreationReviewModal
          isOpen={projectReviewModalOpen}
          onClose={() => {
            setProjectReviewModalOpen(false);
            setProjectReviewData(null);
          }}
          project={projectReviewData.project}
          tasks={projectReviewData.tasks}
          onCommit={handleProjectCommit}
          isCommitting={isCommittingProject}
        />
      )}

      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-white">PMO Agent Outputs</h2>
          <p className="text-sm text-gray-400 mt-1">
            All agent outputs related to PMO projects, showing task completion proof and which Agent team carried out each task. Select an output to view details, thinking process, and generated documents.
          </p>
        </div>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search outputs..."
            className="pl-8 bg-gray-800 border-gray-700 text-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full aspect-square p-0 hover:bg-gray-700"
              onClick={() => setSearchTerm('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Agent Outputs ({filteredOutputs.length})
            </h3>
            {filteredOutputs.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p className="text-sm">
                  {outputs.length === 0
                    ? "No PMO-related agent outputs found"
                    : "No outputs match your search criteria"}
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-[600px] overflow-y-auto pr-2 custom-scrollbar">
                {filteredOutputs.map((output) => {
                  const agentInfo = getAgentTypeInfo(output.agentType);
                  return (
                    <div
                      key={output.id}
                      onClick={() => handleOutputSelect(output)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedOutput?.id === output.id
                          ? 'bg-purple-600/20 border border-purple-500'
                          : 'bg-gray-700/50 hover:bg-gray-700 border border-transparent'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium text-white ${agentInfo.color}`}>
                            {agentInfo.name}
                          </span>
                          <span className="text-xs text-gray-400">
                            {formatTimestamp(getOutputTimestamp(output))}
                          </span>
                        </div>
                      </div>
                      <h4 className="text-sm font-medium text-white mb-1 line-clamp-2">
                        {getOutputTitle(output)}
                      </h4>
                      <p className="text-xs text-gray-400 line-clamp-2">
                        {getOutputContent(output).substring(0, 100)}...
                      </p>
                      {output.fileUrl && (
                        <div className="mt-2 flex items-center text-xs text-blue-400">
                          <Download className="h-3 w-3 mr-1" />
                          PDF Available
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
            {hasMore && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  onClick={loadMoreOutputs}
                  disabled={loadingMore}
                  className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                >
                  {loadingMore ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="md:col-span-2 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          {selectedOutput ? (
            <div>
              <div className="mb-4 pb-4 border-b border-gray-700">
                <h3 className="text-xl font-medium text-purple-200 mb-2">
                  {getOutputTitle(selectedOutput)}
                </h3>
                <div className="flex flex-wrap gap-y-2 text-sm text-gray-400">
                  <div className="flex items-center mr-4">
                    <Clock className="h-4 w-4 mr-1 text-purple-300" />
                    {formatTimestamp(getOutputTimestamp(selectedOutput))}
                  </div>
                  <div className="flex items-center mr-4">
                    <User className="h-4 w-4 mr-1 text-purple-300" />
                    <span className="capitalize">{getAgentTypeInfo(selectedOutput.agentType).team}</span>
                  </div>
                  <div className="flex items-center mr-4">
                    <Tag className="h-4 w-4 mr-1 text-purple-300" />
                    <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getAgentTypeInfo(selectedOutput.agentType).color}`}>
                      {getAgentTypeInfo(selectedOutput.agentType).name}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Settings className="h-4 w-4 mr-1 text-purple-300" />
                    <span className="text-xs font-mono bg-gray-700 px-2 py-1 rounded">
                      Agent Output ID: {selectedOutput.id}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mb-4 flex space-x-2">
                {selectedOutput.fileUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(selectedOutput.fileUrl, '_blank')}
                    className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download PDF
                  </Button>
                )}
                {(selectedOutput.agentType?.includes('PMO') &&
                  getOutputTitle(selectedOutput).includes('Requirements') &&
                  selectedOutput.metadata?.assignedTeam) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => triggerStrategicPlan(selectedOutput)}
                    disabled={isCreatingStrategicPlan}
                    className="bg-green-600 hover:bg-green-700 text-white border-green-600"
                  >
                    {isCreatingStrategicPlan ? (
                      <> <Loader2 className="h-4 w-4 mr-1 animate-spin" /> Creating... </>
                    ) : (
                      <> <Play className="h-4 w-4 mr-1" /> Create Strategic Plan </>
                    )}
                  </Button>
                )}
                {((selectedOutput.agentType === 'strategic-director' ||
                   selectedOutput.agentType === 'research-strategic-director' ||
                   selectedOutput.agentType === 'research-team' ||
                   selectedOutput.agentType === 'ResearchAgentManager' ||
                   selectedOutput.agentType === 'Research') &&
                  selectedOutput.metadata?.pmoId) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => triggerProjectCreationReview(selectedOutput)}
                    disabled={isLoadingProjectPreview}
                    className="bg-orange-600 hover:bg-orange-700 text-white border-orange-600"
                  >
                    {isLoadingProjectPreview ? (
                      <> <Loader2 className="h-4 w-4 mr-1 animate-spin" /> Loading... </>
                    ) : (
                      <> <Settings className="h-4 w-4 mr-1" /> Create Project </>
                    )}
                  </Button>
                )}
              </div>

              <div className="mb-4">
                <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                  Content
                  <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                </h4>
                <div
                  className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[400px] custom-scrollbar cursor-pointer hover:bg-gray-700 transition-colors"
                  onClick={() => openOutputModal(
                    `${getAgentTypeInfo(selectedOutput.agentType).name} Output`,
                    getOutputContent(selectedOutput),
                    'output'
                  )}
                >
                  <MarkdownRenderer content={getOutputContent(selectedOutput)} />
                </div>
              </div>

              {selectedOutput.sources && selectedOutput.sources.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                    Query Documents — {getAgentTypeInfo(selectedOutput.agentType).name}
                    <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                  </h4>
                  <div
                    className="p-3 bg-gray-700/50 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors"
                    onClick={() => openOutputModal(
                      `Query Documents — ${getAgentTypeInfo(selectedOutput.agentType).name}`,
                      getQueryDocumentsContent(selectedOutput),
                      'query-documents'
                    )}
                  >
                    <p className="text-sm text-gray-300 mb-3">Found the following information:</p>
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-yellow-300">• Directly relevant information:</div>
                      <ul className="space-y-1 ml-4">
                        {selectedOutput.sources.map((source, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <div className="w-1.5 h-1.5 rounded-full bg-purple-400 mt-1.5 mr-2 flex-shrink-0"></div>
                            <div>
                              <span className="font-medium text-purple-200">{source.title}</span>
                              {source.page && <span className="text-gray-400"> (Page {source.page})</span>}
                              {source.relevance && (
                                <span className="text-gray-400 ml-2">
                                  (Relevance: {Math.round(source.relevance * 100)}%)
                                </span>
                              )}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {selectedOutput.isCollaboration && selectedOutput.agentMessages && selectedOutput.agentMessages.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                    Strategic Analysis Collaboration
                    <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                  </h4>
                  <div
                    className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[300px] custom-scrollbar space-y-3 cursor-pointer hover:bg-gray-700 transition-colors"
                    onClick={() => {
                      // Combine all messages into a single markdown string
                      const collaborationContent = (selectedOutput.agentMessages || [])
                        .filter(message =>
                          (message.from === 'strategic-director' && message.to === 'user') ||
                          (message.from === 'user' && message.to === 'strategic-director') ||
                          (message.message && message.message.length > 100)
                        )
                        .map(message => {
                          const fromLabel = message.from === 'user' ? 'You' :
                            message.from.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                          const toLabel = message.to === 'user' ? 'You' :
                            message.to.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                          return `### ${fromLabel} → ${toLabel}\n\n${message.message}\n\n---\n\n`;
                        })
                        .join('');

                      openOutputModal(
                        'Strategic Analysis Collaboration',
                        collaborationContent,
                        'collaboration'
                      );
                    }}
                  >
                    {/* Filter out internal messages and only show key interactions */}
                    {selectedOutput.agentMessages
                      .filter(message =>
                        // Only show messages between strategic-director and user
                        // or messages with substantial content
                        (message.from === 'strategic-director' && message.to === 'user') ||
                        (message.from === 'user' && message.to === 'strategic-director') ||
                        (message.message && message.message.length > 100)
                      )
                      .map((message, index) => (
                        <div key={index} className={`p-2 border rounded-lg ${
                          message.from === 'strategic-director' && message.to === 'user'
                            ? 'border-purple-600/50 bg-purple-900/20'
                            : 'border-gray-600'
                        }`}>
                          <div className="flex items-center mb-1 text-sm">
                            <span className={`font-medium ${
                              message.from === 'strategic-director' ? 'text-purple-300' :
                              message.from === 'user' ? 'text-blue-300' : 'text-gray-300'
                            }`}>
                              {message.from === 'user' ? 'You' :
                               message.from.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                            </span>
                            <span className="mx-2 text-gray-400">→</span>
                            <span className={`font-medium ${
                              message.to === 'strategic-director' ? 'text-purple-300' :
                              message.to === 'user' ? 'text-blue-300' : 'text-gray-300'
                            }`}>
                              {message.to === 'user' ? 'You' :
                               message.to.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                            </span>
                          </div>
                          <div className="text-sm text-gray-300">
                            <MarkdownRenderer content={message.message} />
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              )}

              {/* Debug Panel - Toggle with button */}
              {showDebugPanel && (
                <div className="mb-4 p-3 bg-red-900/20 border border-red-600/50 rounded-lg">
                <h4 className="text-sm font-medium text-red-300 mb-2">Debug: QA Analysis Data</h4>
                <div className="text-xs text-gray-300 space-y-1">
                  <div>Has direct qaAnalysisResult: {selectedOutput.qaAnalysisResult ? 'YES' : 'NO'}</div>
                  <div>Has agentInteractions: {selectedOutput.agentInteractions ? 'YES' : 'NO'}</div>
                  <div>Has agentInteractions.qaAnalysisResult: {selectedOutput.agentInteractions?.qaAnalysisResult ? 'YES' : 'NO'}</div>
                  <div>Helper function result: {getQAAnalysisResult(selectedOutput) ? 'FOUND' : 'NOT FOUND'}</div>

                  {selectedOutput.qaAnalysisResult && (
                    <>
                      <div className="text-yellow-300">Direct qaAnalysisResult:</div>
                      <div>- Has questions: {selectedOutput.qaAnalysisResult.questions ? 'YES' : 'NO'}</div>
                      <div>- Questions length: {selectedOutput.qaAnalysisResult.questions?.length || 0}</div>
                      <div>- ConversationId: {selectedOutput.qaAnalysisResult.conversationId || 'None'}</div>
                    </>
                  )}

                  {selectedOutput.agentInteractions?.qaAnalysisResult && (
                    <>
                      <div className="text-green-300">AgentInteractions qaAnalysisResult:</div>
                      <div>- Has questions: {selectedOutput.agentInteractions.qaAnalysisResult.questions ? 'YES' : 'NO'}</div>
                      <div>- Questions length: {selectedOutput.agentInteractions.qaAnalysisResult.questions?.length || 0}</div>
                      <div>- ConversationId: {selectedOutput.agentInteractions.qaAnalysisResult.conversationId || 'None'}</div>
                    </>
                  )}

                  <details className="mt-2">
                    <summary className="cursor-pointer text-red-300">Raw selectedOutput keys</summary>
                    <pre className="mt-1 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-32">
                      {JSON.stringify(Object.keys(selectedOutput), null, 2)}
                    </pre>
                  </details>
                  <details className="mt-2">
                    <summary className="cursor-pointer text-red-300">Raw agentInteractions</summary>
                    <pre className="mt-1 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-32">
                      {JSON.stringify(selectedOutput.agentInteractions, null, 2)}
                    </pre>
                  </details>
                </div>
                </div>
              )}

              {/* QA Analysis Results Panel */}
              {getQAAnalysisResult(selectedOutput) && (
                <div className="mb-4">
                  <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                    QA Analysis Results
                    <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDebugPanel(!showDebugPanel)}
                      className="ml-4 bg-red-600 hover:bg-red-700 text-white border-red-600"
                    >
                      <Search className="h-4 w-4 mr-1" />
                      {showDebugPanel ? 'Hide Debug' : 'Show Debug'}
                    </Button>
                  </h4>
                  <div
                    className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[400px] custom-scrollbar cursor-pointer hover:bg-gray-700 transition-colors"
                    onClick={() => openOutputModal(
                      'QA Analysis Results',
                      getQAAnalysisContent(selectedOutput),
                      'qa-analysis'
                    )}
                  >
                    <div className="space-y-4">
                      {getQAAnalysisResult(selectedOutput)!.questions!.map((qa: any, index: number) => (
                        <div key={index} className="border-b border-gray-600 last:border-b-0 pb-3 last:pb-0">
                          <div className="mb-2">
                            <div className="flex items-start">
                              <span className="inline-flex items-center justify-center w-6 h-6 bg-purple-600 text-white text-xs font-medium rounded-full mr-3 mt-0.5 flex-shrink-0">
                                Q{index + 1}
                              </span>
                              <div className="text-sm font-medium text-purple-200 leading-relaxed">
                                {qa.question || qa.answer?.question || 'Question not available'}
                              </div>
                            </div>
                          </div>
                          <div className="ml-9">
                            <div className="flex items-start mb-2">
                              <span className="inline-flex items-center justify-center w-6 h-6 bg-green-600 text-white text-xs font-medium rounded-full mr-3 mt-0.5 flex-shrink-0">
                                A
                              </span>
                              <div className="text-sm text-gray-300 leading-relaxed">
                                <MarkdownRenderer content={qa.answer?.answer || qa.answer || 'Answer not available'} />
                              </div>
                            </div>
                            {qa.answer?.conversationId && (
                              <div className="ml-9 text-xs text-gray-500">
                                Conversation ID: {qa.answer.conversationId}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    {getQAAnalysisResult(selectedOutput)!.conversationId && (
                      <div className="mt-4 pt-3 border-t border-gray-600">
                        <div className="text-xs text-gray-400">
                          <span className="font-medium">Analysis Session:</span> {getQAAnalysisResult(selectedOutput)!.conversationId}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {selectedOutput.metadata && Object.keys(selectedOutput.metadata).length > 0 && (
                <div>
                  <h4 className="text-md font-medium text-purple-300 mb-2">Metadata</h4>
                  <div className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[300px] custom-scrollbar">
                    <div className="space-y-2">
                      {Object.entries(selectedOutput.metadata).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-2 py-1 border-b border-gray-600 last:border-b-0">
                          <div className="font-medium text-gray-300 capitalize text-sm break-words">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </div>
                          <div className="col-span-2 text-gray-400 text-sm break-words">
                            {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full py-16 text-center text-gray-400">
              <FileText className="h-16 w-16 mb-4 opacity-20 text-purple-400" />
              <p className="text-lg mb-2">Select an output to view details</p>
              <p className="text-sm max-w-md">
                Agent outputs include content, metadata, and any generated documents from all teams working on PMO projects.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}