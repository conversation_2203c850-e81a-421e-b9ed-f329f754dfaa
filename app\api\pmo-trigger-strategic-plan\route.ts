import { NextRequest, NextResponse } from 'next/server';
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';

/**
 * API endpoint to trigger strategic plan creation by the appropriate team
 * This is called from the PMO Output tab when a user wants to invoke team strategic planning
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription,
      pmoAssessment,
      teamSelectionRationale,
      priority,
      category,
      userId,
      requirementsDocument
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    let result;

    // Route to appropriate team agent based on teamId
    switch (teamId) {
      case AgenticTeamId.Marketing:
      case 'Marketing':
        // Create Marketing Strategic Director Agent instance
        const marketingAgent = new StrategicDirectorAgent(
          'strategic-director',
          'Strategic Director',
          userId,
          'anthropic', // default provider
          'claude-sonnet-4-20250514' // default model
        );

        // Create strategic plan using Marketing expertise
        result = await marketingAgent.createPMOStrategicPlan({
          pmoId,
          projectTitle,
          projectDescription: projectDescription || '',
          pmoAssessment: pmoAssessment || '',
          teamSelectionRationale: teamSelectionRationale || '',
          priority: priority || 'Medium',
          category: category || `PMO - ${projectTitle} - ${pmoId}`,
          requirementsDocument
        });
        break;

      case AgenticTeamId.Research:
      case 'Research':
        // Create Research Agent Manager instance
        const { ResearchAgentManager } = await import('../../../lib/agents/research/ResearchAgentManager');
        const researchManager = new ResearchAgentManager({
          userId,
          defaultLlmProvider: 'anthropic', // default provider
          defaultLlmModel: 'claude-sonnet-4-20250514' // default model
        });

        // Initialize the research team
        await researchManager.initializeResearchTeam();

        // Create strategic plan using Research expertise
        result = await researchManager.createPMOStrategicPlan({
          pmoId,
          projectTitle,
          projectDescription: projectDescription || '',
          pmoAssessment: pmoAssessment || '',
          teamSelectionRationale: teamSelectionRationale || '',
          priority: priority || 'Medium',
          category: category || `PMO - ${projectTitle} - ${pmoId}`,
          requirementsDocument
        });
        break;

      case AgenticTeamId.SoftwareDesign:
      case 'SoftwareDesign':
      case 'Software Design':
        // TODO: Implement Software Design team strategic planning
        return NextResponse.json({
          success: false,
          error: 'Software Design team strategic planning not yet implemented'
        }, { status: 501 });

      case AgenticTeamId.Sales:
      case 'Sales':
        // TODO: Implement Sales team strategic planning
        return NextResponse.json({
          success: false,
          error: 'Sales team strategic planning not yet implemented'
        }, { status: 501 });

      case AgenticTeamId.BusinessAnalysis:
      case 'BusinessAnalysis':
      case 'Business Analysis':
        // TODO: Implement Business Analysis team strategic planning
        return NextResponse.json({
          success: false,
          error: 'Business Analysis team strategic planning not yet implemented'
        }, { status: 501 });

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown team: ${teamName} (${teamId})`
        }, { status: 400 });
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to create strategic plan');
    }

    return NextResponse.json({
      success: true,
      teamName,
      documentTitle: result.documentTitle,
      message: `Strategic implementation plan created successfully by ${teamName} team`,
      strategicPlan: result.strategicPlan
    });

  } catch (error) {
    console.error('Error triggering strategic plan creation:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to trigger strategic plan creation'
      },
      { status: 500 }
    );
  }
}
